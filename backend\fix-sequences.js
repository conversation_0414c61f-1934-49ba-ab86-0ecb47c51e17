// Fix Database Sequences Script
// This script fixes sequence issues that can cause duplicate key errors

const { fixSequences } = require('./database');

async function runSequenceFix() {
  try {
    console.log('Starting sequence fix...');
    await fixSequences();
    console.log('✅ All sequences have been fixed successfully!');
    console.log('You can now try generating receipts again.');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error fixing sequences:', error);
    process.exit(1);
  }
}

// Run the fix
runSequenceFix();
