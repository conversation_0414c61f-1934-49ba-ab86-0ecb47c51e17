// models/salesModel.js
const pool = require('../database');

const salesModel = {
  createSaleRecord: async (saleData) => {
    try {
      const query = `
        INSERT INTO sales (
          reference_number, date, billing_name, billing_address,
          billing_email, billing_phone, shipping_name, shipping_address,
          shipping_email, shipping_phone, payment_method, subtotal,
          tax, total, total_profit, salesperson_name, company_name
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        RETURNING id
      `;

      const values = [
        saleData.reference_number,
        saleData.date,
        saleData.billing_name,
        saleData.billing_address,
        saleData.billing_email,
        saleData.billing_phone,
        saleData.shipping_name,
        saleData.shipping_address,
        saleData.shipping_email,
        saleData.shipping_phone,
        saleData.payment_method,
        saleData.subtotal,
        saleData.tax,
        saleData.total,
        saleData.total_profit || 0,
        saleData.salesperson_name || '',
        saleData.company_name || 'Shans Accessories PTY LTD'
      ];

      const { rows } = await pool.query(query, values);
      return rows[0]?.id || null;
    } catch (error) {
      console.error('Error in createSaleRecord:', error);
      throw error;
    }
  },

  createSaleItem: async (itemData) => {
    try {
      const query = `
        INSERT INTO sale_items (
          sale_id, item_code, item_name, quantity,
          unit_price_excluding_tax, unit_cost, profit_per_unit,
          total_profit, tax_per_product, total_price
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `;

      const values = [
        itemData.sale_id,
        itemData.item_code,
        itemData.item_name,
        itemData.quantity,
        itemData.unit_price_excluding_tax,
        itemData.unit_cost || 0,
        itemData.profit_per_unit || 0,
        itemData.total_profit || 0,
        itemData.tax_per_product,
        itemData.total_price
      ];

      const { rows } = await pool.query(query, values);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in createSaleItem:', error);
      throw error;
    }
  },

  getAllSales: async () => {
    try {
      const query = `
        SELECT
          s.id AS sale_id,
          s.reference_number,
          s.date,
          s.billing_name,
          s.billing_address,
          s.billing_email,
          s.billing_phone,
          s.shipping_name,
          s.shipping_address,
          s.shipping_email,
          s.shipping_phone,
          s.payment_method,
          s.subtotal,
          s.tax,
          s.total,
          s.total_profit AS total_profit,
          s.salesperson_name,
          s.company_name,
          si.item_code,
          si.item_name,
          si.quantity,
          si.unit_price_excluding_tax,
          si.unit_cost,
          si.profit_per_unit,
          si.total_profit AS item_total_profit,
          si.tax_per_product,
          si.total_price,
          s.created_at
        FROM sales s
        LEFT JOIN sale_items si ON s.id = si.sale_id
        ORDER BY s.date DESC, s.id DESC
      `;

      const { rows } = await pool.query(query);

      // Group the results by sale
      const salesMap = new Map();

      rows.forEach(row => {
        if (!salesMap.has(row.sale_id)) {
          // Create new sale entry
          salesMap.set(row.sale_id, {
            sale_id: row.sale_id,
            reference_number: row.reference_number,
            date: row.date,
            billing_name: row.billing_name,
            billing_address: row.billing_address,
            billing_email: row.billing_email,
            billing_phone: row.billing_phone,
            shipping_name: row.shipping_name,
            shipping_address: row.shipping_address,
            shipping_email: row.shipping_email,
            shipping_phone: row.shipping_phone,
            payment_method: row.payment_method,
            subtotal: row.subtotal,
            tax: row.tax,
            total: row.total,
            total_profit: row.total_profit || 0,
            salesperson_name: row.salesperson_name,
            company_name: row.company_name || 'Shans Accessories PTY LTD',
            created_at: row.created_at,
            items: []
          });
        }

        // Add item to sale if it exists
        if (row.item_code) {
          salesMap.get(row.sale_id).items.push({
            item_code: row.item_code,
            item_name: row.item_name,
            quantity: row.quantity,
            unit_price_excluding_tax: row.unit_price_excluding_tax,
            unit_cost: row.unit_cost || 0,
            profit_per_unit: row.profit_per_unit || 0,
            total_profit: row.item_total_profit || 0,
            tax_per_product: row.tax_per_product,
            total_price: row.total_price
          });
        }
      });

      return Array.from(salesMap.values());
    } catch (error) {
      console.error('Error in getAllSales:', error);
      throw error;
    }
  },

  getSaleById: async (saleId) => {
    try {
      const query = `
        SELECT
          s.id AS sale_id,
          s.reference_number,
          s.date,
          s.billing_name,
          s.billing_address,
          s.billing_email,
          s.billing_phone,
          s.shipping_name,
          s.shipping_address,
          s.shipping_email,
          s.shipping_phone,
          s.payment_method,
          s.subtotal,
          s.tax,
          s.total,
          s.total_profit AS total_profit,
          s.salesperson_name,
          s.company_name,
          si.item_code,
          si.item_name,
          si.quantity,
          si.unit_price_excluding_tax,
          si.unit_cost,
          si.profit_per_unit,
          si.total_profit AS item_total_profit,
          si.tax_per_product,
          si.total_price,
          s.created_at
        FROM sales s
        LEFT JOIN sale_items si ON s.id = si.sale_id
        WHERE s.id = $1
      `;

      const { rows } = await pool.query(query, [saleId]);

      if (rows.length === 0) {
        return null;
      }

      // Construct the sale object with items
      const sale = {
        sale_id: rows[0].sale_id,
        reference_number: rows[0].reference_number,
        date: rows[0].date,
        billing_name: rows[0].billing_name,
        billing_address: rows[0].billing_address,
        billing_email: rows[0].billing_email,
        billing_phone: rows[0].billing_phone,
        shipping_name: rows[0].shipping_name,
        shipping_address: rows[0].shipping_address,
        shipping_email: rows[0].shipping_email,
        shipping_phone: rows[0].shipping_phone,
        payment_method: rows[0].payment_method,
        subtotal: rows[0].subtotal,
        tax: rows[0].tax,
        total: rows[0].total,
        total_profit: rows[0].total_profit || 0,
        salesperson_name: rows[0].salesperson_name,
        company_name: rows[0].company_name || 'Shans Accessories PTY LTD',
        created_at: rows[0].created_at,
        items: rows.map(row => ({
          item_code: row.item_code,
          item_name: row.item_name,
          quantity: row.quantity,
          unit_price_excluding_tax: row.unit_price_excluding_tax,
          unit_cost: row.unit_cost || 0,
          profit_per_unit: row.profit_per_unit || 0,
          total_profit: row.item_total_profit || 0,
          tax_per_product: row.tax_per_product,
          total_price: row.total_price
        })).filter(item => item.item_code) // Filter out null items from LEFT JOIN
      };

      return sale;
    } catch (error) {
      console.error('Error in getSaleById:', error);
      throw error;
    }
  },

  getSaleByReferenceNumber: async (referenceNumber) => {
    try {
      const query = `
        SELECT
          s.id AS sale_id,
          s.reference_number,
          s.date,
          s.billing_name,
          s.billing_address,
          s.billing_email,
          s.billing_phone,
          s.shipping_name,
          s.shipping_address,
          s.shipping_email,
          s.shipping_phone,
          s.payment_method,
          s.subtotal,
          s.tax,
          s.total,
          s.total_profit AS total_profit,
          s.salesperson_name,
          s.company_name,
          si.item_code,
          si.item_name,
          si.quantity,
          si.unit_price_excluding_tax,
          si.unit_cost,
          si.profit_per_unit,
          si.total_profit AS item_total_profit,
          si.tax_per_product,
          si.total_price,
          s.created_at
        FROM sales s
        LEFT JOIN sale_items si ON s.id = si.sale_id
        WHERE s.reference_number = $1
      `;

      const { rows } = await pool.query(query, [referenceNumber]);

      if (rows.length === 0) {
        return null;
      }

      // Construct the sale object with items
      const sale = {
        sale_id: rows[0].sale_id,
        reference_number: rows[0].reference_number,
        date: rows[0].date,
        billing_name: rows[0].billing_name,
        billing_address: rows[0].billing_address,
        billing_email: rows[0].billing_email,
        billing_phone: rows[0].billing_phone,
        shipping_name: rows[0].shipping_name,
        shipping_address: rows[0].shipping_address,
        shipping_email: rows[0].shipping_email,
        shipping_phone: rows[0].shipping_phone,
        payment_method: rows[0].payment_method,
        subtotal: rows[0].subtotal,
        tax: rows[0].tax,
        total: rows[0].total,
        total_profit: rows[0].total_profit || 0,
        salesperson_name: rows[0].salesperson_name,
        company_name: rows[0].company_name || 'Shans Accessories PTY LTD',
        created_at: rows[0].created_at,
        items: rows.map(row => ({
          item_code: row.item_code,
          item_name: row.item_name,
          quantity: row.quantity,
          unit_price_excluding_tax: row.unit_price_excluding_tax,
          unit_cost: row.unit_cost || 0,
          profit_per_unit: row.profit_per_unit || 0,
          total_profit: row.item_total_profit || 0,
          tax_per_product: row.tax_per_product,
          total_price: row.total_price
        })).filter(item => item.item_code)
      };

      return sale;
    } catch (error) {
      console.error('Error in getSaleByReferenceNumber:', error);
      throw error;
    }
  }
};

module.exports = salesModel;