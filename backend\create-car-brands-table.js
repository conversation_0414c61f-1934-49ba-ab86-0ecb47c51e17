// Manual script to create the car_brands table
require('dotenv').config();
const { Pool } = require('pg');

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
});

async function createCarBrandsTable() {
  const client = await pool.connect();
  try {
    console.log('Creating car_brands table...');
    
    // Create the car_brands table
    await client.query(`
      CREATE TABLE IF NOT EXISTS car_brands (
        id SERIAL PRIMARY KEY,
        brand_id TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    console.log('Car brands table created successfully.');
    
    // Insert default car brands
    const defaultBrands = [
      { brand_id: 'toyota', name: 'Toyota' },
      { brand_id: 'honda', name: 'Honda' },
      { brand_id: 'ford', name: 'Ford' },
      { brand_id: 'chevrolet', name: 'Chevrolet' },
      { brand_id: 'bmw', name: 'BMW' },
      { brand_id: 'mercedes_benz', name: 'Mercedes-Benz' },
      { brand_id: 'audi', name: 'Audi' },
      { brand_id: 'volkswagen', name: 'Volkswagen' },
      { brand_id: 'nissan', name: 'Nissan' },
      { brand_id: 'hyundai', name: 'Hyundai' },
      { brand_id: 'kia', name: 'Kia' },
      { brand_id: 'mazda', name: 'Mazda' },
      { brand_id: 'subaru', name: 'Subaru' },
      { brand_id: 'lexus', name: 'Lexus' },
      { brand_id: 'jeep', name: 'Jeep' },
      { brand_id: 'tesla', name: 'Tesla' }
    ];
    
    console.log('Inserting default car brands...');
    
    for (const brand of defaultBrands) {
      await client.query(`
        INSERT INTO car_brands (brand_id, name)
        VALUES ($1, $2)
        ON CONFLICT (brand_id) DO NOTHING;
      `, [brand.brand_id, brand.name]);
    }
    
    console.log('Default car brands inserted successfully.');
    
  } catch (error) {
    console.error('Error creating car_brands table:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the function
createCarBrandsTable()
  .then(() => {
    console.log('Car brands table setup completed.');
    // Add a small delay before exiting to ensure all DB operations complete
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  })
  .catch((error) => {
    console.error('Failed to set up car brands table:', error);
    process.exit(1);
  });
