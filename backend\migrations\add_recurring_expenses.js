// migrations/add_recurring_expenses.js
const pool = require('../database');

async function addRecurringExpenseColumns() {
  let client;
  
  try {
    client = await pool.connect();
    
    console.log('Starting migration: Adding recurring expense columns...');
    
    // Check if columns already exist
    const checkColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'expenses' 
      AND column_name IN ('is_recurring', 'recurring_template_id')
    `);
    
    const existingColumns = checkColumns.rows.map(row => row.column_name);
    
    // Add is_recurring column if it doesn't exist
    if (!existingColumns.includes('is_recurring')) {
      await client.query(`
        ALTER TABLE expenses 
        ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE
      `);
      console.log('Added is_recurring column');
    } else {
      console.log('is_recurring column already exists');
    }
    
    // Add recurring_template_id column if it doesn't exist
    if (!existingColumns.includes('recurring_template_id')) {
      await client.query(`
        ALTER TABLE expenses 
        ADD COLUMN recurring_template_id INTEGER
      `);
      console.log('Added recurring_template_id column');
      
      // Add foreign key constraint
      await client.query(`
        ALTER TABLE expenses 
        ADD CONSTRAINT fk_recurring_template 
        FOREIGN KEY (recurring_template_id) REFERENCES expenses(id) ON DELETE SET NULL
      `);
      console.log('Added foreign key constraint for recurring_template_id');
    } else {
      console.log('recurring_template_id column already exists');
    }
    
    console.log('Migration completed successfully!');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    if (client) {
      client.release();
    }
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  addRecurringExpenseColumns()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { addRecurringExpenseColumns };
