// backend/services/openaiService.js
const OpenAI = require('openai');
const pdfParse = require('pdf-parse');

// Initialize OpenAI client
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************'
});

const openaiService = {
    /**
     * Extract expense data from receipt/slip image or PDF
     * @param {Buffer} fileBuffer - The file buffer
     * @param {string} mimeType - The MIME type of the file
     * @returns {Promise<Object>} Extracted data containing company name, amount, and item name
     */
    extractExpenseData: async (fileBuffer, mimeType) => {
        try {
            console.log(`[${new Date().toISOString()}] Starting OpenAI expense data extraction for ${mimeType}`);

            let extractedData = {
                companyName: '',
                amount: '',
                itemName: '',
                confidence: 0,
                error: null
            };

            if (mimeType.startsWith('image/')) {
                // Handle image files
                extractedData = await extractFromImage(fileBuffer, mimeType);
            } else if (mimeType === 'application/pdf') {
                // Handle PDF files
                extractedData = await extractFromPDF(fileBuffer);
            } else {
                throw new Error('Unsupported file type for data extraction');
            }

            console.log(`[${new Date().toISOString()}] OpenAI extraction completed:`, {
                companyName: extractedData.companyName,
                amount: extractedData.amount,
                itemName: extractedData.itemName,
                confidence: extractedData.confidence
            });

            return extractedData;

        } catch (error) {
            console.error('OpenAI extraction error:', error);

            // Check if it's a quota/billing error
            if (error.status === 429 || error.code === 'insufficient_quota') {
                console.log('OpenAI quota exceeded, using mock extraction for demo purposes');
                return getMockExtractionResult(mimeType);
            }

            return {
                companyName: '',
                amount: '',
                itemName: '',
                confidence: 0,
                error: error.message
            };
        }
    }
};

/**
 * Extract data from image using OpenAI Vision API
 */
async function extractFromImage(fileBuffer, mimeType) {
    try {
        // Convert buffer to base64
        const base64Image = fileBuffer.toString('base64');
        const dataUrl = `data:${mimeType};base64,${base64Image}`;

        const response = await openai.chat.completions.create({
            model: "gpt-4o",
            messages: [
                {
                    role: "system",
                    content: `You are an expert at extracting expense information from receipts and invoices.
                    You must analyze the image and extract the business name, total amount, and main item/service.
                    Always respond with ONLY a valid JSON object in this exact format:
                    {"companyName": "business name", "amount": 123.45, "itemName": "item description", "confidence": 95}

                    Rules:
                    - companyName: Extract the business/store name (e.g., "Woolworths", "Pick n Pay")
                    - amount: Extract ONLY the total amount as a number (e.g., 125.50, not "R125.50")
                    - itemName: Describe the main item/service (e.g., "Groceries", "Fuel", "Office Supplies")
                    - confidence: Your confidence level from 0-100
                    - If you cannot find information, use empty string "" for text fields and 0 for numbers
                    - Return ONLY the JSON object, no other text`
                },
                {
                    role: "user",
                    content: [
                        {
                            type: "text",
                            text: `Please analyze this receipt image and extract the expense information. Return only the JSON object with companyName, amount, itemName, and confidence.`
                        },
                        {
                            type: "image_url",
                            image_url: {
                                url: dataUrl,
                                detail: "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens: 500,
            temperature: 0.1
        });

        const content = response.choices[0]?.message?.content;
        if (!content) {
            throw new Error('No response from OpenAI');
        }

        console.log('Raw OpenAI response:', content);

        // Clean and parse the response with multiple fallback strategies
        let extractedData;
        let cleanedContent = content.trim();

        try {
            // Strategy 1: Try direct JSON parse
            extractedData = JSON.parse(cleanedContent);
        } catch (parseError1) {
            console.log('Direct JSON parse failed, trying cleanup strategies...');

            try {
                // Strategy 2: Remove markdown code blocks
                if (cleanedContent.startsWith('```json')) {
                    cleanedContent = cleanedContent.replace(/```json\s*/, '').replace(/\s*```$/, '');
                } else if (cleanedContent.startsWith('```')) {
                    cleanedContent = cleanedContent.replace(/```\s*/, '').replace(/\s*```$/, '');
                }
                extractedData = JSON.parse(cleanedContent);
            } catch (parseError2) {
                console.log('Markdown cleanup failed, trying regex extraction...');

                try {
                    // Strategy 3: Extract JSON object using regex
                    const jsonMatch = cleanedContent.match(/\{[^}]*\}/);
                    if (jsonMatch) {
                        extractedData = JSON.parse(jsonMatch[0]);
                    } else {
                        throw new Error('No JSON object found in response');
                    }
                } catch (parseError3) {
                    console.log('Regex extraction failed, trying manual parsing...');

                    // Strategy 4: Manual extraction as fallback
                    extractedData = extractDataManually(cleanedContent);
                }
            }
        }

        console.log('Parsed extracted data:', extractedData);

        // Validate and clean the extracted data with better handling
        const result = {
            companyName: extractedData.companyName ? String(extractedData.companyName).trim() : '',
            amount: extractedData.amount ? cleanAmount(extractedData.amount) : '',
            itemName: extractedData.itemName ? String(extractedData.itemName).trim() : '',
            confidence: extractedData.confidence ? Math.min(100, Math.max(0, Number(extractedData.confidence))) : 0,
            error: null
        };

        console.log('Final processed extraction result:', result);

        // Validate that we got at least some data
        if (!result.companyName && !result.amount && !result.itemName) {
            console.warn('No data extracted from image, this might indicate an issue with the image or prompt');
            result.confidence = 0;
            result.error = 'No readable data found in the image';
        }

        return result;

    } catch (error) {
        console.error('Image extraction error:', error);

        // Check if it's a quota/billing error
        if (error.status === 429 || error.code === 'insufficient_quota') {
            console.log('OpenAI quota exceeded, returning mock data for demo');
            return getMockExtractionResult('image');
        }

        throw new Error(`Failed to extract data from image: ${error.message}`);
    }
}

/**
 * Extract data from PDF by parsing text content and using OpenAI for analysis
 */
async function extractFromPDF(fileBuffer) {
    try {
        console.log('Processing PDF file for real data extraction...');

        // Step 1: Extract text from PDF with enhanced error handling
        console.log('Extracting text from PDF...');
        let extractedText = '';

        try {
            const pdfData = await pdfParse(fileBuffer, {
                // PDF parsing options for better compatibility
                max: 0, // No page limit
                version: 'v1.10.100'
            });
            extractedText = pdfData.text;

            console.log('PDF text extracted successfully. Length:', extractedText.length, 'characters');
            if (extractedText.length > 0) {
                console.log('PDF text preview:', extractedText.substring(0, 200) + '...');
            }

        } catch (pdfParseError) {
            console.error('PDF parsing failed:', pdfParseError.message);

            // If PDF parsing fails, provide helpful guidance
            return {
                companyName: '',
                amount: '',
                itemName: '',
                confidence: 0,
                error: `Unable to extract text from PDF: ${pdfParseError.message}. This PDF may be image-based or corrupted. Please try converting to an image (JPG/PNG) for better results.`,
                isPdfProcessed: false,
                suggestion: 'For best results, use image formats (JPG, PNG) or ensure your PDF contains selectable text.'
            };
        }

        if (!extractedText || extractedText.trim().length < 10) {
            console.warn('PDF contains very little or no extractable text');
            return {
                companyName: '',
                amount: '',
                itemName: '',
                confidence: 0,
                error: 'PDF contains no readable text. This may be an image-based PDF. Please try converting to an image (JPG/PNG) for better results.',
                isPdfProcessed: false,
                suggestion: 'Scan or screenshot the receipt and save as JPG/PNG for optimal AI extraction.'
            };
        }

        // Step 2: Use OpenAI to analyze the extracted text
        console.log('Analyzing extracted PDF text with OpenAI...');
        const response = await openai.chat.completions.create({
            model: "gpt-4o",
            messages: [
                {
                    role: "system",
                    content: `You are an expert at extracting expense information from receipt text.
                    Analyze the provided text and extract the business name, total amount, and main item/service.
                    Always respond with ONLY a valid JSON object in this exact format:
                    {"companyName": "business name", "amount": 123.45, "itemName": "item description", "confidence": 95}

                    Rules:
                    - companyName: Extract the business/store name (e.g., "Woolworths", "Pick n Pay")
                    - amount: Extract ONLY the total amount as a number (e.g., 125.50, not "R125.50")
                    - itemName: Describe the main item/service (e.g., "Groceries", "Fuel", "Office Supplies")
                    - confidence: Your confidence level from 0-100 based on text clarity
                    - If you cannot find information, use empty string "" for text fields and 0 for numbers
                    - Return ONLY the JSON object, no other text`
                },
                {
                    role: "user",
                    content: `Please analyze this receipt text and extract the expense information. Return only the JSON object with companyName, amount, itemName, and confidence.

Receipt text:
${extractedText}`
                }
            ],
            max_tokens: 500,
            temperature: 0.1
        });

        const content = response.choices[0]?.message?.content;
        if (!content) {
            throw new Error('No response from OpenAI for PDF text analysis');
        }

        console.log('Raw OpenAI response for PDF:', content);

        // Parse the response using the same logic as image extraction
        let extractedData;
        let cleanedContent = content.trim();

        try {
            // Try direct JSON parse
            extractedData = JSON.parse(cleanedContent);
        } catch (parseError1) {
            console.log('Direct JSON parse failed for PDF, trying cleanup strategies...');

            try {
                // Remove markdown code blocks
                if (cleanedContent.startsWith('```json')) {
                    cleanedContent = cleanedContent.replace(/```json\s*/, '').replace(/\s*```$/, '');
                } else if (cleanedContent.startsWith('```')) {
                    cleanedContent = cleanedContent.replace(/```\s*/, '').replace(/\s*```$/, '');
                }
                extractedData = JSON.parse(cleanedContent);
            } catch (parseError2) {
                console.log('Markdown cleanup failed for PDF, trying regex extraction...');

                try {
                    // Extract JSON object using regex
                    const jsonMatch = cleanedContent.match(/\{[^}]*\}/);
                    if (jsonMatch) {
                        extractedData = JSON.parse(jsonMatch[0]);
                    } else {
                        throw new Error('No JSON object found in PDF response');
                    }
                } catch (parseError3) {
                    console.log('Regex extraction failed for PDF, trying manual parsing...');
                    extractedData = extractDataManually(cleanedContent);
                }
            }
        }

        console.log('Parsed PDF extraction data:', extractedData);

        // Validate and clean the extracted data
        const result = {
            companyName: extractedData.companyName ? String(extractedData.companyName).trim() : '',
            amount: extractedData.amount ? cleanAmount(extractedData.amount) : '',
            itemName: extractedData.itemName ? String(extractedData.itemName).trim() : '',
            confidence: extractedData.confidence ? Math.min(100, Math.max(0, Number(extractedData.confidence))) : 0,
            error: null,
            isPdfProcessed: true
        };

        console.log('Final PDF extraction result:', result);

        // Validate that we got at least some data
        if (!result.companyName && !result.amount && !result.itemName) {
            console.warn('No data extracted from PDF text, this might indicate an issue with the content');
            result.confidence = 0;
            result.error = 'No readable expense data found in the PDF text';
        }

        return result;

    } catch (error) {
        console.error('PDF extraction error:', error);

        // Check if it's a quota/billing error
        if (error.status === 429 || error.code === 'insufficient_quota') {
            console.log('OpenAI quota exceeded during PDF processing, returning mock data');
            return getMockExtractionResult('pdf');
        }

        // Provide helpful guidance for PDF issues
        return {
            companyName: '',
            amount: '',
            itemName: '',
            confidence: 0,
            error: `PDF processing failed: ${error.message}. Please try converting your PDF to an image (JPG/PNG) for better results.`,
            isPdfProcessed: false,
            suggestion: 'For optimal AI extraction, use clear images of receipts in JPG or PNG format.'
        };
    }
}

/**
 * Clean and validate amount string
 */
function cleanAmount(amount) {
    if (!amount) return '';
    
    // Remove currency symbols and extra spaces
    const cleaned = amount.toString()
        .replace(/[R$€£¥₹]/g, '') // Remove common currency symbols
        .replace(/[^\d.,]/g, '') // Keep only digits, dots, and commas
        .trim();
    
    // Convert comma decimal separator to dot if needed
    const normalized = cleaned.replace(',', '.');
    
    // Validate it's a valid number
    const parsed = parseFloat(normalized);
    if (isNaN(parsed)) return '';
    
    return parsed.toString();
}

/**
 * Manual extraction fallback when JSON parsing fails
 */
function extractDataManually(content) {
    console.log('Attempting manual extraction from content:', content);

    const result = {
        companyName: '',
        amount: '',
        itemName: '',
        confidence: 0
    };

    // Try to extract company name (look for common patterns)
    const companyPatterns = [
        /company[:\s]+([^\n\r,]+)/i,
        /store[:\s]+([^\n\r,]+)/i,
        /business[:\s]+([^\n\r,]+)/i,
        /([A-Z][a-z]+\s*[A-Z]*[a-z]*)\s*(?:store|shop|market|pharmacy|restaurant)/i
    ];

    for (const pattern of companyPatterns) {
        const match = content.match(pattern);
        if (match && match[1]) {
            result.companyName = match[1].trim();
            break;
        }
    }

    // Try to extract amount (look for currency patterns)
    const amountPatterns = [
        /total[:\s]*[R$€£¥₹]?\s*(\d+\.?\d*)/i,
        /amount[:\s]*[R$€£¥₹]?\s*(\d+\.?\d*)/i,
        /[R$€£¥₹]\s*(\d+\.?\d*)/,
        /(\d+\.\d{2})/
    ];

    for (const pattern of amountPatterns) {
        const match = content.match(pattern);
        if (match && match[1]) {
            result.amount = match[1];
            break;
        }
    }

    // Try to extract item name (look for common item patterns)
    const itemPatterns = [
        /item[:\s]+([^\n\r,]+)/i,
        /product[:\s]+([^\n\r,]+)/i,
        /service[:\s]+([^\n\r,]+)/i,
        /(groceries|fuel|food|supplies|medicine|clothing)/i
    ];

    for (const pattern of itemPatterns) {
        const match = content.match(pattern);
        if (match && match[1]) {
            result.itemName = match[1].trim();
            break;
        }
    }

    // Set confidence based on how much data we found
    let foundFields = 0;
    if (result.companyName) foundFields++;
    if (result.amount) foundFields++;
    if (result.itemName) foundFields++;

    result.confidence = Math.round((foundFields / 3) * 100);

    console.log('Manual extraction result:', result);
    return result;
}

/**
 * Generate mock extraction result for demo purposes when OpenAI quota is exceeded
 */
function getMockExtractionResult(mimeType) {
    console.log('Generating mock extraction result for demo purposes');

    // Simulate realistic extraction results
    const mockResults = [
        {
            companyName: 'Woolworths',
            amount: '125.50',
            itemName: 'Groceries',
            confidence: 85
        },
        {
            companyName: 'Pick n Pay',
            amount: '89.99',
            itemName: 'Household Items',
            confidence: 90
        },
        {
            companyName: 'Checkers',
            amount: '67.25',
            itemName: 'Food & Beverages',
            confidence: 78
        },
        {
            companyName: 'Spar',
            amount: '45.80',
            itemName: 'Office Supplies',
            confidence: 82
        },
        {
            companyName: 'Clicks',
            amount: '156.75',
            itemName: 'Health & Beauty',
            confidence: 88
        }
    ];

    // Select a random mock result
    const randomResult = mockResults[Math.floor(Math.random() * mockResults.length)];

    return {
        companyName: randomResult.companyName,
        amount: randomResult.amount,
        itemName: randomResult.itemName,
        confidence: randomResult.confidence,
        error: null,
        isMockData: true // Flag to indicate this is demo data
    };
}

module.exports = openaiService;
