// models/carBrandModel.js
const pool = require('../database');

const carBrandModel = {
  createBrand: async (brand) => {
    const { brand_id, name } = brand;
    const query = 'INSERT INTO car_brands (brand_id, name) VALUES ($1, $2) RETURNING *';
    const values = [brand_id, name];
    
    try {
      const { rows } = await pool.query(query, values);
      return rows[0];
    } catch (error) {
      console.error('Error in createBrand:', error);
      throw error;
    }
  },

  getAllBrands: async () => {
    try {
      const query = 'SELECT * FROM car_brands ORDER BY name';
      const { rows } = await pool.query(query);
      return rows;
    } catch (error) {
      console.error('Error in getAllBrands:', error);
      throw error;
    }
  },

  getBrandById: async (brand_id) => {
    try {
      const query = 'SELECT * FROM car_brands WHERE brand_id = $1';
      const { rows } = await pool.query(query, [brand_id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in getBrandById:', error);
      throw error;
    }
  },

  updateBrand: async (brand_id, name) => {
    try {
      const query = 'UPDATE car_brands SET name = $1 WHERE brand_id = $2 RETURNING *';
      const { rows } = await pool.query(query, [name, brand_id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in updateBrand:', error);
      throw error;
    }
  },

  deleteBrand: async (brand_id) => {
    try {
      const query = 'DELETE FROM car_brands WHERE brand_id = $1 RETURNING *';
      const { rows } = await pool.query(query, [brand_id]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteBrand:', error);
      throw error;
    }
  },

  // Check if any products are using this brand
  isBrandInUse: async (brand_id) => {
    try {
      const query = 'SELECT COUNT(*) FROM products WHERE car_brand = $1';
      const { rows } = await pool.query(query, [brand_id]);
      return parseInt(rows[0].count) > 0;
    } catch (error) {
      console.error('Error in isBrandInUse:', error);
      throw error;
    }
  }
};

module.exports = carBrandModel;
