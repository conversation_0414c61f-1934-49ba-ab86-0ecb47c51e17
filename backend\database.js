// database.js
const { Pool } = require('pg');
require('dotenv').config();

// Create PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }, // Always use SSL for Render database
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

console.log('PostgreSQL database pool initialized successfully');

// PostgreSQL pool already has the query method and connect method built-in
// No need for custom executeQuery function - PostgreSQL handles everything natively

// Initialize database tables with retry logic
async function initializeTables(retryCount = 5, delay = 5000) {
  let attempts = 0;

  while (attempts < retryCount) {
    let client;
    let clientReleased = false;

    try {
      console.log(`Attempting to connect to database (attempt ${attempts + 1}/${retryCount})...`);
      client = await pool.connect();

      // Execute table creation queries for PostgreSQL
      await client.query(`
        CREATE TABLE IF NOT EXISTS rooms (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          color VARCHAR(50) NOT NULL,
          location VARCHAR(255) NOT NULL
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS products (
          item_code VARCHAR(100) PRIMARY KEY,
          room_id INTEGER NOT NULL,
          item_name VARCHAR(255) NOT NULL,
          car_brand VARCHAR(100) NOT NULL,
          car_model VARCHAR(100) NOT NULL,
          unit_retail_price DECIMAL(10,2) NOT NULL,
          wholesale_price DECIMAL(10,2) NOT NULL,
          unit_cost DECIMAL(10,2) NOT NULL,
          supplier_code VARCHAR(100) NOT NULL,
          available_stock INTEGER NOT NULL,
          location VARCHAR(255) NOT NULL,
          colour_tape VARCHAR(50) NOT NULL,
          profit DECIMAL(10,2) NOT NULL,
          additional_comments TEXT,
          product_category VARCHAR(100) NOT NULL,
          min_order_quantity INTEGER DEFAULT 1,
          low_stock_threshold INTEGER DEFAULT 5,
          FOREIGN KEY(room_id) REFERENCES rooms(id) ON DELETE CASCADE
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS sales (
          id SERIAL PRIMARY KEY,
          reference_number VARCHAR(100) NOT NULL UNIQUE,
          date VARCHAR(50) NOT NULL,
          billing_name VARCHAR(255) NOT NULL,
          billing_address TEXT NOT NULL,
          billing_email VARCHAR(255) NOT NULL,
          billing_phone VARCHAR(50),
          shipping_name VARCHAR(255),
          shipping_address TEXT,
          shipping_email VARCHAR(255),
          shipping_phone VARCHAR(50),
          payment_method VARCHAR(100),
          subtotal DECIMAL(10,2) NOT NULL,
          tax DECIMAL(10,2) NOT NULL,
          total DECIMAL(10,2) NOT NULL,
          total_profit DECIMAL(10,2),
          salesperson_name VARCHAR(255),
          company_name VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS sale_items (
          id SERIAL PRIMARY KEY,
          sale_id INTEGER NOT NULL,
          item_code VARCHAR(100) NOT NULL,
          item_name VARCHAR(255) NOT NULL,
          quantity INTEGER NOT NULL,
          unit_price_excluding_tax DECIMAL(10,2) NOT NULL,
          unit_cost DECIMAL(10,2),
          profit_per_unit DECIMAL(10,2),
          total_profit DECIMAL(10,2),
          tax_per_product DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          FOREIGN KEY(sale_id) REFERENCES sales(id)
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          is_admin BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS expense_categories (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100) NOT NULL UNIQUE,
          description TEXT,
          color VARCHAR(7) DEFAULT '#3498db',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS expenses (
          id SERIAL PRIMARY KEY,
          category_id INTEGER NOT NULL,
          name VARCHAR(255) NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          description TEXT,
          expense_date DATE NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY(category_id) REFERENCES expense_categories(id) ON DELETE CASCADE
        )
      `);

      // Add recurring columns if they don't exist (for existing databases)
      try {
        // Check if is_recurring column exists
        const recurringColumnCheck = await client.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'expenses' AND column_name = 'is_recurring'
        `);

        if (recurringColumnCheck.rows.length === 0) {
          await client.query(`ALTER TABLE expenses ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE`);
          console.log('Added is_recurring column to expenses table');
        }

        // Check if recurring_template_id column exists
        const templateIdColumnCheck = await client.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'expenses' AND column_name = 'recurring_template_id'
        `);

        if (templateIdColumnCheck.rows.length === 0) {
          await client.query(`ALTER TABLE expenses ADD COLUMN recurring_template_id INTEGER`);
          console.log('Added recurring_template_id column to expenses table');

          // Add foreign key constraint
          await client.query(`
            ALTER TABLE expenses
            ADD CONSTRAINT fk_recurring_template
            FOREIGN KEY (recurring_template_id) REFERENCES expenses(id) ON DELETE SET NULL
          `);
          console.log('Added foreign key constraint for recurring_template_id');
        }

        // Check if media_url column exists
        const mediaUrlColumnCheck = await client.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'expenses' AND column_name = 'media_url'
        `);

        if (mediaUrlColumnCheck.rows.length === 0) {
          await client.query(`ALTER TABLE expenses ADD COLUMN media_url TEXT`);
          console.log('Added media_url column to expenses table');
        }

        // Check if min_order_quantity column exists in products table
        const minOrderQuantityCheck = await client.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'products' AND column_name = 'min_order_quantity'
        `);

        if (minOrderQuantityCheck.rows.length === 0) {
          await client.query(`ALTER TABLE products ADD COLUMN min_order_quantity INTEGER DEFAULT 1`);
          console.log('Added min_order_quantity column to products table');
        }

        // Check if low_stock_threshold column exists in products table
        const lowStockThresholdCheck = await client.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'products' AND column_name = 'low_stock_threshold'
        `);

        if (lowStockThresholdCheck.rows.length === 0) {
          await client.query(`ALTER TABLE products ADD COLUMN low_stock_threshold INTEGER DEFAULT 5`);
          console.log('Added low_stock_threshold column to products table');
        }
      } catch (columnError) {
        console.log('Note: Could not add recurring columns (they may already exist):', columnError.message);
      }

      // Insert default expense categories if they don't exist
      const defaultCategories = [
        { name: 'Operating Expenses', description: 'Day-to-day operational costs', color: '#e74c3c' },
        { name: 'Rent', description: 'Property rental costs', color: '#3498db' },
        { name: 'Salaries', description: 'Employee compensation', color: '#2ecc71' },
        { name: 'Utilities', description: 'Electricity, water, internet, etc.', color: '#f39c12' },
        { name: 'Insurance', description: 'Business insurance premiums', color: '#9b59b6' }
      ];

      for (const category of defaultCategories) {
        await client.query(`
          INSERT INTO expense_categories (name, description, color)
          VALUES ($1, $2, $3)
          ON CONFLICT (name) DO NOTHING
        `, [category.name, category.description, category.color]);
      }

      await client.query(`
        CREATE TABLE IF NOT EXISTS categories (
          id SERIAL PRIMARY KEY,
          category_id VARCHAR(100) NOT NULL UNIQUE,
          name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS car_brands (
          id SERIAL PRIMARY KEY,
          brand_id VARCHAR(100) NOT NULL UNIQUE,
          name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS car_models (
          id SERIAL PRIMARY KEY,
          model_id VARCHAR(100) NOT NULL UNIQUE,
          name VARCHAR(255) NOT NULL,
          brand_id VARCHAR(100) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create quotations table
      await client.query(`
        CREATE TABLE IF NOT EXISTS quotations (
          id SERIAL PRIMARY KEY,
          reference_number VARCHAR(255) NOT NULL UNIQUE,
          date DATE NOT NULL,
          billing_name VARCHAR(255) NOT NULL,
          billing_address TEXT,
          billing_email VARCHAR(255) NOT NULL,
          billing_phone VARCHAR(50),
          shipping_name VARCHAR(255),
          shipping_address TEXT,
          shipping_email VARCHAR(255),
          shipping_phone VARCHAR(50),
          payment_method VARCHAR(100) NOT NULL,
          subtotal DECIMAL(10, 2) NOT NULL,
          tax DECIMAL(10, 2) NOT NULL,
          total DECIMAL(10, 2) NOT NULL,
          salesperson_name VARCHAR(255),
          company_name VARCHAR(255) DEFAULT 'Shans Accessories PTY LTD',
          comments TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create quotation_items table
      await client.query(`
        CREATE TABLE IF NOT EXISTS quotation_items (
          id SERIAL PRIMARY KEY,
          quotation_id INTEGER NOT NULL,
          item_code VARCHAR(255) NOT NULL,
          item_name VARCHAR(255) NOT NULL,
          quantity INTEGER NOT NULL,
          unit_price_excluding_tax DECIMAL(10, 2) NOT NULL,
          tax_per_product DECIMAL(10, 2) NOT NULL,
          total_price DECIMAL(10, 2) NOT NULL,
          FOREIGN KEY (quotation_id) REFERENCES quotations(id) ON DELETE CASCADE
        )
      `);

      // Create invoices table
      await client.query(`
        CREATE TABLE IF NOT EXISTS invoices (
          id SERIAL PRIMARY KEY,
          reference_number VARCHAR(255) NOT NULL UNIQUE,
          date DATE NOT NULL,
          billing_name VARCHAR(255) NOT NULL,
          billing_address TEXT,
          billing_email VARCHAR(255) NOT NULL,
          billing_phone VARCHAR(50),
          shipping_name VARCHAR(255),
          shipping_address TEXT,
          shipping_email VARCHAR(255),
          shipping_phone VARCHAR(50),
          payment_method VARCHAR(100) NOT NULL,
          subtotal DECIMAL(10, 2) NOT NULL,
          tax DECIMAL(10, 2) NOT NULL,
          total DECIMAL(10, 2) NOT NULL,
          salesperson_name VARCHAR(255),
          company_name VARCHAR(255) DEFAULT 'Shans Accessories PTY LTD',
          comments TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create invoice_items table
      await client.query(`
        CREATE TABLE IF NOT EXISTS invoice_items (
          id SERIAL PRIMARY KEY,
          invoice_id INTEGER NOT NULL,
          item_code VARCHAR(255) NOT NULL,
          item_name VARCHAR(255) NOT NULL,
          quantity INTEGER NOT NULL,
          unit_price_excluding_tax DECIMAL(10, 2) NOT NULL,
          tax_per_product DECIMAL(10, 2) NOT NULL,
          total_price DECIMAL(10, 2) NOT NULL,
          FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
        )
      `);

      // Create recurring_expense_exclusions table for month isolation
      await client.query(`
        CREATE TABLE IF NOT EXISTS recurring_expense_exclusions (
          id SERIAL PRIMARY KEY,
          recurring_template_id INTEGER NOT NULL,
          year INTEGER NOT NULL,
          month INTEGER NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (recurring_template_id) REFERENCES expenses(id) ON DELETE CASCADE,
          UNIQUE(recurring_template_id, year, month)
        )
      `);

      // Create recurring_expense_exclusions table for month isolation
      await client.query(`
        CREATE TABLE IF NOT EXISTS recurring_expense_exclusions (
          id SERIAL PRIMARY KEY,
          recurring_template_id INTEGER NOT NULL,
          year INTEGER NOT NULL,
          month INTEGER NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (recurring_template_id) REFERENCES expenses(id) ON DELETE CASCADE,
          UNIQUE(recurring_template_id, year, month)
        )
      `);

      // Create recurring_expense_exclusions table for month isolation
      await client.query(`
        CREATE TABLE IF NOT EXISTS recurring_expense_exclusions (
          id SERIAL PRIMARY KEY,
          recurring_template_id INTEGER NOT NULL,
          year INTEGER NOT NULL,
          month INTEGER NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (recurring_template_id) REFERENCES expenses(id) ON DELETE CASCADE,
          UNIQUE(recurring_template_id, year, month)
        )
      `);

      // Mark that we're about to release the client
      clientReleased = true;
      client.release();

      console.log('Database tables initialized successfully');
      return; // Success, exit the function
    } catch (error) {
      attempts++;
      console.error(`Error initializing database tables (attempt ${attempts}/${retryCount}):`, error);

      if (attempts >= retryCount) {
        console.error('Maximum retry attempts reached. Could not initialize database tables.');
        throw error;
      }

      console.log(`Retrying in ${delay/1000} seconds...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    } finally {
      // Only release the client if it exists and hasn't been released yet
      if (client && !clientReleased) {
        try {
          client.release();
        } catch (releaseErr) {
          console.error('Error releasing client:', releaseErr.message);
        }
      }
    }
  }
}

// Function to fix sequence issues for all SERIAL columns
async function fixSequences() {
  let client;
  try {
    client = await pool.connect();

    // Fix sequences for all tables with SERIAL primary keys
    const sequenceFixes = [
      "SELECT setval('rooms_id_seq', COALESCE((SELECT MAX(id) FROM rooms), 0) + 1, false)",
      "SELECT setval('sales_id_seq', COALESCE((SELECT MAX(id) FROM sales), 0) + 1, false)",
      "SELECT setval('sale_items_id_seq', COALESCE((SELECT MAX(id) FROM sale_items), 0) + 1, false)",
      "SELECT setval('users_id_seq', COALESCE((SELECT MAX(id) FROM users), 0) + 1, false)",
      "SELECT setval('expense_categories_id_seq', COALESCE((SELECT MAX(id) FROM expense_categories), 0) + 1, false)",
      "SELECT setval('categories_id_seq', COALESCE((SELECT MAX(id) FROM categories), 0) + 1, false)",
      "SELECT setval('car_brands_id_seq', COALESCE((SELECT MAX(id) FROM car_brands), 0) + 1, false)",
      "SELECT setval('car_models_id_seq', COALESCE((SELECT MAX(id) FROM car_models), 0) + 1, false)",
      "SELECT setval('expenses_id_seq', COALESCE((SELECT MAX(id) FROM expenses), 0) + 1, false)",
      "SELECT setval('quotations_id_seq', COALESCE((SELECT MAX(id) FROM quotations), 0) + 1, false)",
      "SELECT setval('quotation_items_id_seq', COALESCE((SELECT MAX(id) FROM quotation_items), 0) + 1, false)",
      "SELECT setval('invoices_id_seq', COALESCE((SELECT MAX(id) FROM invoices), 0) + 1, false)",
      "SELECT setval('invoice_items_id_seq', COALESCE((SELECT MAX(id) FROM invoice_items), 0) + 1, false)"
    ];

    for (const sequenceFix of sequenceFixes) {
      try {
        await client.query(sequenceFix);
      } catch (error) {
        // Ignore errors for sequences that don't exist yet
        if (!error.message.includes('does not exist')) {
          console.warn('Warning fixing sequence:', error.message);
        }
      }
    }

    console.log('Database sequences fixed successfully');
  } catch (error) {
    console.error('Error fixing sequences:', error);
  } finally {
    if (client) {
      client.release();
    }
  }
}

// Initialize tables when the application starts with a more robust approach
(async () => {
  try {
    await initializeTables();
    await fixSequences(); // Fix any sequence issues after table initialization
    console.log('Database initialization completed successfully');
  } catch (error) {
    console.error('Failed to initialize database after multiple attempts:', error);
    // The application can still run even if table initialization fails
    // as the tables might already exist from previous runs
    console.log('Continuing application startup despite database initialization failure');
  }
})();

// Test database connection with retry logic
async function testDatabaseConnection(retryCount = 5, delay = 1000) {
  let attempts = 0;

  while (attempts < retryCount) {
    let client;
    let clientReleased = false;

    try {
      console.log(`Testing database connection (attempt ${attempts + 1}/${retryCount})...`);
      client = await pool.connect();

      const result = await client.query('SELECT NOW() as now');
      console.log('Database connected successfully at:', result.rows[0].now);

      // Mark that we're about to release the client
      clientReleased = true;
      client.release();

      return true;
    } catch (err) {
      attempts++;
      console.error(`Error connecting to database (attempt ${attempts}/${retryCount}):`, err.message);

      if (attempts >= retryCount) {
        console.error('Maximum retry attempts reached. Could not connect to database.');
        return false;
      }

      console.log(`Retrying connection in ${delay/1000} seconds...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    } finally {
      // Only release the client if it exists and hasn't been released yet
      if (client && !clientReleased) {
        try {
          client.release();
        } catch (releaseErr) {
          console.error('Error releasing client:', releaseErr.message);
        }
      }
    }
  }
  return false;
}

// Execute the test connection
testDatabaseConnection().then(success => {
  if (!success) {
    console.warn('WARNING: Database connection test failed, but application will continue to run.');
    console.warn('Some database operations may fail until connection is restored.');
  }
});

module.exports = pool;
module.exports.initializeTables = initializeTables;
module.exports.fixSequences = fixSequences;