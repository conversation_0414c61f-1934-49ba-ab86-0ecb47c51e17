// Migration to add profit-related columns to sale_items table
require('dotenv').config();
const { Pool } = require('pg');

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
});

async function addProfitColumnsToSaleItems() {
  const client = await pool.connect();
  try {
    console.log('Starting migration: Adding profit-related columns to sale_items table...');
    
    // Check if the unit_cost column already exists
    const checkUnitCostQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'sale_items' AND column_name = 'unit_cost';
    `;
    
    const unitCostResult = await client.query(checkUnitCostQuery);
    
    if (unitCostResult.rows.length === 0) {
      // Column doesn't exist, so add it
      const addUnitCostQuery = `
        ALTER TABLE sale_items 
        ADD COLUMN unit_cost DECIMAL DEFAULT 0;
      `;
      
      await client.query(addUnitCostQuery);
      console.log('Migration successful: unit_cost column added to sale_items table.');
    } else {
      console.log('Column unit_cost already exists in sale_items table. No changes made.');
    }
    
    // Check if the profit_per_unit column already exists
    const checkProfitPerUnitQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'sale_items' AND column_name = 'profit_per_unit';
    `;
    
    const profitPerUnitResult = await client.query(checkProfitPerUnitQuery);
    
    if (profitPerUnitResult.rows.length === 0) {
      // Column doesn't exist, so add it
      const addProfitPerUnitQuery = `
        ALTER TABLE sale_items 
        ADD COLUMN profit_per_unit DECIMAL DEFAULT 0;
      `;
      
      await client.query(addProfitPerUnitQuery);
      console.log('Migration successful: profit_per_unit column added to sale_items table.');
    } else {
      console.log('Column profit_per_unit already exists in sale_items table. No changes made.');
    }
    
    // Check if the total_profit column already exists
    const checkTotalProfitQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'sale_items' AND column_name = 'total_profit';
    `;
    
    const totalProfitResult = await client.query(checkTotalProfitQuery);
    
    if (totalProfitResult.rows.length === 0) {
      // Column doesn't exist, so add it
      const addTotalProfitQuery = `
        ALTER TABLE sale_items 
        ADD COLUMN total_profit DECIMAL DEFAULT 0;
      `;
      
      await client.query(addTotalProfitQuery);
      console.log('Migration successful: total_profit column added to sale_items table.');
    } else {
      console.log('Column total_profit already exists in sale_items table. No changes made.');
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration
addProfitColumnsToSaleItems()
  .then(() => {
    console.log('Migration completed.');
    // Add a small delay before exiting to ensure all DB operations complete
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
