// backend/routes/backupRoutes.js
const express = require('express');
const { v2: cloudinary } = require('cloudinary');
const XLSX = require('xlsx');
const multer = require('multer');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads (store in memory)
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        // Accept only Excel files
        if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.originalname.endsWith('.xlsx')) {
            cb(null, true);
        } else {
            cb(new Error('Only Excel files (.xlsx) are allowed'), false);
        }
    }
});

// Validate environment variables
if (!process.env.CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
    console.error('❌ Missing Cloudinary environment variables!');
    console.error('Please ensure CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, and CLOUDINARY_API_SECRET are set in your .env file');
    throw new Error('Missing required Cloudinary environment variables');
}

// Configure Cloudinary with environment variables
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
});

// Helper function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Simple test route to verify routes are working
router.get('/test-route', (req, res) => {
    res.json({ message: 'Backup routes are working!', timestamp: new Date().toISOString() });
});

// Test route with authentication
router.get('/test-auth', authenticateToken, (req, res) => {
    res.json({
        message: 'Authentication working!',
        user: req.user.email,
        isAdmin: req.user.is_admin,
        timestamp: new Date().toISOString()
    });
});

// NEW: POST endpoint for uploading Excel files directly
router.post('/upload-excel-backup', authenticateToken, upload.single('excelFile'), async (req, res) => {
    console.log(`[${new Date().toISOString()}] Excel upload endpoint hit by user: ${req.user?.email || 'unknown'}`);
    console.log('Request body keys:', Object.keys(req.body));
    console.log('File received:', req.file ? 'Yes' : 'No');

    try {
        const { monthYear } = req.body;
        const file = req.file;

        if (!file) {
            return res.status(400).json({ error: 'No Excel file provided' });
        }

        if (!monthYear) {
            return res.status(400).json({ error: 'Month/Year is required' });
        }

        console.log(`[${new Date().toISOString()}] Uploading Excel file for ${monthYear} by user ${req.user.email}`);
        console.log(`File details: ${file.originalname}, Size: ${file.size} bytes`);

        // Create filename with timestamp
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `Sales_Backup_${monthYear.replace(/\s+/g, '_')}_${timestamp}.xlsx`;
        const publicId = `sales_backups/${filename}`;

        console.log(`[${new Date().toISOString()}] Uploading ${filename} to Cloudinary...`);

        // Upload Excel file directly to Cloudinary
        const uploadResult = await new Promise((resolve, reject) => {
            const uploadStream = cloudinary.uploader.upload_stream(
                {
                    resource_type: 'raw',
                    public_id: publicId,
                    folder: 'sales_backups',
                    tags: ['sales_backup', 'excel', monthYear.replace(/\s+/g, '_')],
                    context: {
                        month: monthYear,
                        backup_date: new Date().toISOString(),
                        original_filename: file.originalname,
                        backed_up_by: req.user.email
                    }
                },
                (error, result) => {
                    if (error) {
                        console.error('Cloudinary upload error:', error);
                        reject(error);
                    } else {
                        console.log(`[${new Date().toISOString()}] Successfully uploaded to Cloudinary: ${result.secure_url}`);
                        resolve(result);
                    }
                }
            );

            // Write the file buffer to the upload stream
            uploadStream.end(file.buffer);
        });

        res.json({
            success: true,
            message: `Excel backup successful for ${monthYear}`,
            filename: filename,
            url: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            uploadedAt: new Date().toISOString(),
            fileSize: file.size
        });

    } catch (error) {
        console.error('Excel upload error:', error);
        res.status(500).json({
            error: 'Failed to upload Excel backup',
            details: error.message
        });
    }
});

// ORIGINAL: POST endpoint for backing up sales data (keeping for backward compatibility)
router.post('/backup-sales-data', authenticateToken, async (req, res) => {
    try {
        const { monthYear, salesData } = req.body;
        
        // Validate input
        if (!monthYear || !salesData || !Array.isArray(salesData)) {
            return res.status(400).json({ 
                error: 'Invalid input. monthYear and salesData array required.' 
            });
        }

        console.log(`[${new Date().toISOString()}] Starting backup for ${monthYear} by user ${req.user.email}`);
        
        // Process and flatten the sales data
        const flattenedData = salesData.flatMap(sale =>
            sale.items.map(item => {
                const quantity = parseFloat(item.quantity) || 0;
                const totalPrice = parseFloat(item.total_price) || 0;
                const unitPrice = parseFloat(item.unit_price_excluding_tax) || (quantity > 0 ? totalPrice / quantity : 0);
                const unitCost = parseFloat(item.unit_cost) || 0;
                const profitPerUnit = unitPrice - unitCost;
                const totalProfit = profitPerUnit * quantity;
                const totalRetailValue = unitPrice * quantity;
                const totalCost = unitCost * quantity;

                return {
                    "Reference Number": sale.reference_number,
                    "Date": formatDate(sale.date),
                    "Client Name": sale.billing_name,
                    "Billing Address": sale.billing_address,
                    "Billing Email": sale.billing_email,
                    "Billing Phone": sale.billing_phone || 'N/A',
                    "Salesperson": sale.salesperson_name || 'N/A',
                    "Company": sale.company_name || 'Shans Accessories PTY LTD',
                    "Item Purchased": item.item_name,
                    "Room": item.room_name || 'N/A',
                    "Quantity": quantity,
                    "Unit Price (R)": Math.round(unitPrice),
                    "Unit Cost (R)": Math.round(unitCost),
                    "Profit per Unit (R)": Math.round(profitPerUnit),
                    "Tax per Unit (R)": Math.round(parseFloat(item.tax_per_product) || 0),
                    "Total Value (R)": Math.round(totalRetailValue),
                    "Total Cost (R)": Math.round(totalCost),
                    "Total Profit (R)": Math.round(totalProfit),
                    "Total Tax (R)": Math.round(parseFloat(item.tax_per_product || 0) * quantity),
                    "Payment Method": sale.payment_method,
                    "Total Price (R)": Math.round(totalPrice)
                };
            })
        );
        
        console.log(`[${new Date().toISOString()}] Processed ${flattenedData.length} records for backup`);
        
        // Create Excel file
        const worksheet = XLSX.utils.json_to_sheet(flattenedData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, `Sales ${monthYear}`);
        
        // Generate Excel buffer
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
        
        // Create filename
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `Sales_Backup_${monthYear.replace(/\s+/g, '_')}_${timestamp}.xlsx`;
        const publicId = `sales_backups/${filename}`; // Keep the .xlsx extension in the publicId

        console.log(`[${new Date().toISOString()}] Uploading ${filename} to Cloudinary...`);
        
        // Upload to Cloudinary using Promise wrapper
        const uploadResult = await new Promise((resolve, reject) => {
            const uploadStream = cloudinary.uploader.upload_stream(
                {
                    resource_type: 'raw',
                    public_id: publicId,
                    folder: 'sales_backups',
                    tags: ['sales_backup', 'excel', monthYear.replace(/\s+/g, '_')],
                    context: {
                        month: monthYear,
                        backup_date: new Date().toISOString(),
                        record_count: flattenedData.length.toString(),
                        backed_up_by: req.user.email
                    }
                },
                (error, result) => {
                    if (error) {
                        console.error('Cloudinary upload error:', error);
                        reject(error);
                    } else {
                        console.log(`[${new Date().toISOString()}] Successfully uploaded to Cloudinary: ${result.secure_url}`);
                        resolve(result);
                    }
                }
            );
            
            // Write the buffer to the upload stream
            uploadStream.end(excelBuffer);
        });
        
        // Success response
        res.json({
            success: true,
            message: `Backup successful for ${monthYear}`,
            filename: filename,
            url: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            recordCount: flattenedData.length,
            uploadedAt: new Date().toISOString(),
            backedUpBy: req.user.email
        });
        
    } catch (error) {
        console.error('Backup error:', error);
        res.status(500).json({ 
            error: 'Backup failed', 
            details: error.message 
        });
    }
});

// GET endpoint to list all backups
router.get('/list-backups', authenticateToken, async (req, res) => {
    try {
        console.log(`[${new Date().toISOString()}] Listing backups requested by user ${req.user?.email || 'unknown'}`);
        console.log('User object:', req.user);
        console.log('Is admin:', req.user?.is_admin);

        let result;
        try {
            // Try the search API first
            result = await cloudinary.search
                .expression('folder:sales_backups AND tags:sales_backup')
                .sort_by([{created_at: 'desc'}])
                .max_results(100)
                .execute();
        } catch (searchError) {
            console.log('Search API failed, trying admin API fallback:', searchError.message);

            // Fallback to admin API
            result = await cloudinary.api.resources({
                type: 'upload',
                resource_type: 'raw',
                prefix: 'sales_backups/',
                max_results: 100,
                tags: true,
                context: true
            });
        }

        const backups = result.resources.map(resource => {
            const filename = resource.public_id.split('/').pop();
            // Ensure filename has .xlsx extension (for backward compatibility)
            const finalFilename = filename.endsWith('.xlsx') ? filename : filename + '.xlsx';

            return {
                filename: finalFilename,
                url: resource.secure_url,
                uploadedAt: resource.created_at,
                size: resource.bytes,
                month: resource.context?.month || 'Unknown',
                recordCount: resource.context?.record_count || 'Unknown',
                backedUpBy: resource.context?.backed_up_by || 'Unknown'
            };
        });

        // Sort by created_at descending (since admin API doesn't support sorting)
        backups.sort((a, b) => new Date(b.uploadedAt) - new Date(a.uploadedAt));

        console.log(`[${new Date().toISOString()}] Found ${backups.length} backup files`);
        res.json({ backups });

    } catch (error) {
        console.error('List backups error:', error);
        res.status(500).json({
            error: 'Failed to list backups',
            details: error.message
        });
    }
});

// DELETE endpoint to remove a backup
router.delete('/delete-backup/:publicId', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const { publicId } = req.params;
        
        if (!publicId) {
            return res.status(400).json({ error: 'Public ID is required' });
        }
        
        console.log(`[${new Date().toISOString()}] Deleting backup ${publicId} by admin ${req.user.email}`);
        
        const result = await cloudinary.uploader.destroy(publicId, { resource_type: 'raw' });
        
        if (result.result === 'ok') {
            res.json({ 
                success: true, 
                message: 'Backup deleted successfully',
                deletedBy: req.user.email
            });
        } else {
            res.status(404).json({ error: 'Backup not found or already deleted' });
        }
        
    } catch (error) {
        console.error('Delete backup error:', error);
        res.status(500).json({ 
            error: 'Failed to delete backup', 
            details: error.message 
        });
    }
});

// GET endpoint to test Cloudinary connection
router.get('/test-connection', authenticateToken, async (req, res) => {
    try {
        console.log(`[${new Date().toISOString()}] Testing Cloudinary connection for user ${req.user.email}`);
        
        // Create a simple test file
        const testData = JSON.stringify({
            test: true,
            timestamp: new Date().toISOString(),
            message: 'Cloudinary connection test successful',
            testedBy: req.user.email
        });
        
        const testBuffer = Buffer.from(testData, 'utf8');
        const filename = `connection_test_${Date.now()}.json`;
        const publicId = `sales_backups/tests/${filename}`; // Keep the .json extension

        // Upload test file
        const uploadResult = await new Promise((resolve, reject) => {
            const uploadStream = cloudinary.uploader.upload_stream(
                {
                    resource_type: 'raw',
                    public_id: publicId,
                    folder: 'sales_backups/tests',
                    tags: ['test', 'connection_test']
                },
                (error, result) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(result);
                    }
                }
            );
            
            uploadStream.end(testBuffer);
        });
        
        console.log(`[${new Date().toISOString()}] Test file uploaded successfully: ${uploadResult.secure_url}`);
        
        res.json({
            success: true,
            message: 'Cloudinary connection test successful',
            testFile: {
                filename: filename,
                url: uploadResult.secure_url,
                uploadedAt: new Date().toISOString()
            }
        });
        
    } catch (error) {
        console.error('Connection test error:', error);
        res.status(500).json({ 
            error: 'Connection test failed', 
            details: error.message 
        });
    }
});

module.exports = router;
