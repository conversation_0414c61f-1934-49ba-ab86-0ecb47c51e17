/* Add to your existing styles.css */

.room-block button {
    margin: 5px;
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.edit-button {
    background-color: #4CAF50;
    color: white;
}

.delete-button {
    background-color: #f44336;
    color: white;
}

.edit-button:hover {
    background-color: #45a049;
}

.delete-button:hover {
    background-color: #da190b;
}



/* frontend/styles.css */

/* Reset some default browser styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
}

/* Add Product Block */
.add-product-block {
    width: 100px;
    height: 100px;
    background-color: #28a745;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 10px;
    transition: background-color 0.3s;
}

.add-product-block:hover {
    background-color: #218838;
}

.plus-icon {
    color: white;
    font-size: 48px;
}

/* Popup Overlay */
.popup-overlay {
    display: none; /* Hidden by default */
    position: fixed;
    z-index: 1000;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Black w/ opacity */
    justify-content: center;
    align-items: center;
}

.popup-content {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.close-popup {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    background: none;
    border: none;
    cursor: pointer;
}

h2 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="text"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

textarea {
    height: 100px;
    resize: vertical;
}

.color-picker {
    display: flex;
    align-items: center;
}

input[type="color"] {
    width: 50px;
    height: 50px;
    padding: 0;
    border: none;
    border-radius: 50%;
    cursor: pointer;
}

#colorTapeText {
    margin-left: 10px;
    width: calc(100% - 60px);
}

button[type="submit"] {
    display: block;
    width: 100%;
    padding: 10px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

button[type="submit"]:hover {
    background-color: #0056b3;
}

/* Status Message */
.status-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 20px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    display: none;
    z-index: 1001;
}

.status-message.success {
    background-color: #28a745;
}

.status-message.error {
    background-color: #dc3545;
}

/* Products Listing Section */
.products-section {
    width: 100%;
    max-width: 1200px;
    margin: 40px auto 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.products-section h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

#productsTable {
    width: 100%;
    border-collapse: collapse;
}

#productsTable th,
#productsTable td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

#productsTable th {
    background-color: #f2f2f2;
    color: #333;
}

#productsTable tr:nth-child(even) {
    background-color: #f9f9f9;
}

#productsTable tr:hover {
    background-color: #f1f1f1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .popup-content {
        width: 95%;
    }

    .products-section {
        padding: 10px;
    }

    #productsTable th,
    #productsTable td {
        padding: 6px;
        font-size: 14px;
    }
}



/* Existing styles... */

/* Adjust filter-menu to accommodate more filters */
.filter-menu {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

/* Each filter-group */
.filter-group {
    display: flex;
    flex-direction: column;
}

/* Labels within filter-group */
.filter-group label {
    margin-bottom: 5px;
    font-weight: bold;
}

/* Select elements within filter-group */
.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    min-width: 200px;
    /* For Colour Tape, display color swatches */
    display: flex;
    align-items: center;
}

/* Option elements with color swatches for Colour Tape */
.filter-group select#filterColourTape option {
    display: flex;
    align-items: center;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .filter-menu {
        flex-direction: column;
    }

    .filter-group select {
        width: 100%;
    }
}




