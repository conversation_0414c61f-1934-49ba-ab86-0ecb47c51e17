const { Pool } = require('pg');
require('dotenv').config();

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function addSalespersonColumn() {
  const client = await pool.connect();
  try {
    console.log('Starting migration: Adding salesperson_name column to sales table...');
    
    // Check if the column already exists
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'sales' AND column_name = 'salesperson_name';
    `;
    
    const { rows } = await client.query(checkColumnQuery);
    
    if (rows.length === 0) {
      // Column doesn't exist, so add it
      const addColumnQuery = `
        ALTER TABLE sales 
        ADD COLUMN salesperson_name TEXT;
      `;
      
      await client.query(addColumnQuery);
      console.log('Migration successful: salesperson_name column added to sales table.');
    } else {
      console.log('Column salesperson_name already exists in sales table. No changes made.');
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration
addSalespersonColumn()
  .then(() => {
    console.log('Migration completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
