// Migration to add total_profit column to sales table
require('dotenv').config();
const { Pool } = require('pg');

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
});

async function addTotalProfitColumn() {
  const client = await pool.connect();
  try {
    console.log('Starting migration: Adding total_profit column to sales table...');
    
    // Check if the column already exists
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'sales' AND column_name = 'total_profit';
    `;
    
    const { rows } = await client.query(checkColumnQuery);
    
    if (rows.length === 0) {
      // Column doesn't exist, so add it
      const addColumnQuery = `
        ALTER TABLE sales 
        ADD COLUMN total_profit DECIMAL DEFAULT 0;
      `;
      
      await client.query(addColumnQuery);
      console.log('Migration successful: total_profit column added to sales table.');
    } else {
      console.log('Column total_profit already exists in sales table. No changes made.');
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration
addTotalProfitColumn()
  .then(() => {
    console.log('Migration completed.');
    // Add a small delay before exiting to ensure all DB operations complete
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
