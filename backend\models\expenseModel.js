// models/expenseModel.js
const pool = require('../database');

const expenseModel = {
  createExpense: async (expense) => {
    const { category_id, name, amount, description, expense_date, is_recurring, recurring_template_id, media_url } = expense;
    const query = `
      INSERT INTO expenses (category_id, name, amount, description, expense_date, is_recurring, recurring_template_id, media_url)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;
    const values = [
      category_id,
      name,
      amount,
      description || '',
      expense_date,
      is_recurring || false,
      recurring_template_id || null,
      media_url || null
    ];

    try {
      const { rows } = await pool.query(query, values);
      return rows[0];
    } catch (error) {
      console.error('Error in createExpense:', error);
      throw error;
    }
  },

  getAllExpenses: async () => {
    try {
      const query = `
        SELECT e.*, ec.name as category_name, ec.color as category_color
        FROM expenses e
        JOIN expense_categories ec ON e.category_id = ec.id
        ORDER BY e.expense_date DESC, e.created_at DESC
      `;
      const { rows } = await pool.query(query);
      return rows;
    } catch (error) {
      console.error('Error in getAllExpenses:', error);
      throw error;
    }
  },

  getExpensesByCategory: async (category_id) => {
    try {
      const query = `
        SELECT e.*, ec.name as category_name, ec.color as category_color
        FROM expenses e
        JOIN expense_categories ec ON e.category_id = ec.id
        WHERE e.category_id = $1
        ORDER BY e.expense_date DESC, e.created_at DESC
      `;
      const { rows } = await pool.query(query, [category_id]);
      return rows;
    } catch (error) {
      console.error('Error in getExpensesByCategory:', error);
      throw error;
    }
  },

  getExpenseById: async (id) => {
    try {
      const query = `
        SELECT e.*, ec.name as category_name, ec.color as category_color
        FROM expenses e
        JOIN expense_categories ec ON e.category_id = ec.id
        WHERE e.id = $1
      `;
      const { rows } = await pool.query(query, [id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in getExpenseById:', error);
      throw error;
    }
  },

  updateExpense: async (id, expense) => {
    const { category_id, name, amount, description, expense_date, is_recurring, recurring_template_id, media_url } = expense;
    try {
      const query = `
        UPDATE expenses
        SET category_id = $1, name = $2, amount = $3, description = $4, expense_date = $5,
            is_recurring = $6, recurring_template_id = $7, media_url = $8
        WHERE id = $9
        RETURNING *
      `;
      const values = [
        category_id,
        name,
        amount,
        description,
        expense_date,
        is_recurring || false,
        recurring_template_id || null,
        media_url || null,
        id
      ];
      const { rows } = await pool.query(query, values);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in updateExpense:', error);
      throw error;
    }
  },

  deleteExpense: async (id) => {
    try {
      const query = 'DELETE FROM expenses WHERE id = $1 RETURNING *';
      const { rows } = await pool.query(query, [id]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteExpense:', error);
      throw error;
    }
  },

  getTotalExpensesByCategory: async (year = null, month = null) => {
    try {
      let query, values;

      if (year && month) {
        // Month-specific totals with exclusion logic
        query = `
          WITH month_expenses AS (
            -- Get regular expenses for the month
            SELECT e.category_id, e.amount
            FROM expenses e
            WHERE EXTRACT(YEAR FROM e.expense_date) = $1
            AND EXTRACT(MONTH FROM e.expense_date) = $2
            AND NOT (e.is_recurring = true AND e.recurring_template_id IS NULL)

            UNION ALL

            -- Get recurring templates that are NOT excluded for this month
            SELECT e.category_id, e.amount
            FROM expenses e
            LEFT JOIN recurring_expense_exclusions ree ON (
              ree.recurring_template_id = e.id
              AND ree.year = $1
              AND ree.month = $2
            )
            WHERE e.is_recurring = true
            AND e.recurring_template_id IS NULL
            AND ree.id IS NULL
          )
          SELECT
            ec.id,
            ec.name,
            ec.color,
            COALESCE(SUM(me.amount), 0) as total_amount,
            COUNT(me.amount) as expense_count
          FROM expense_categories ec
          LEFT JOIN month_expenses me ON ec.id = me.category_id
          GROUP BY ec.id, ec.name, ec.color
          ORDER BY ec.name
        `;
        values = [year, month];
      } else {
        // All-time totals (original logic)
        query = `
          SELECT
            ec.id,
            ec.name,
            ec.color,
            COALESCE(SUM(e.amount), 0) as total_amount,
            COUNT(e.id) as expense_count
          FROM expense_categories ec
          LEFT JOIN expenses e ON ec.id = e.category_id
          GROUP BY ec.id, ec.name, ec.color
          ORDER BY ec.name
        `;
        values = [];
      }

      const { rows } = await pool.query(query, values);
      return rows;
    } catch (error) {
      console.error('Error in getTotalExpensesByCategory:', error);
      throw error;
    }
  },

  getTotalExpenses: async (year = null, month = null) => {
    try {
      let query, values;

      if (year && month) {
        // Month-specific total with exclusion logic
        query = `
          WITH month_expenses AS (
            -- Get regular expenses for the month
            SELECT e.amount
            FROM expenses e
            WHERE EXTRACT(YEAR FROM e.expense_date) = $1
            AND EXTRACT(MONTH FROM e.expense_date) = $2
            AND NOT (e.is_recurring = true AND e.recurring_template_id IS NULL)

            UNION ALL

            -- Get recurring templates that are NOT excluded for this month
            SELECT e.amount
            FROM expenses e
            LEFT JOIN recurring_expense_exclusions ree ON (
              ree.recurring_template_id = e.id
              AND ree.year = $1
              AND ree.month = $2
            )
            WHERE e.is_recurring = true
            AND e.recurring_template_id IS NULL
            AND ree.id IS NULL
          )
          SELECT COALESCE(SUM(amount), 0) as total FROM month_expenses
        `;
        values = [year, month];
      } else {
        // All-time total (original logic)
        query = 'SELECT COALESCE(SUM(amount), 0) as total FROM expenses';
        values = [];
      }

      const { rows } = await pool.query(query, values);
      return parseFloat(rows[0].total);
    } catch (error) {
      console.error('Error in getTotalExpenses:', error);
      throw error;
    }
  },

  getExpensesByDateRange: async (startDate, endDate) => {
    try {
      const query = `
        SELECT e.*, ec.name as category_name, ec.color as category_color
        FROM expenses e
        JOIN expense_categories ec ON e.category_id = ec.id
        WHERE e.expense_date BETWEEN $1 AND $2
        ORDER BY e.expense_date DESC, e.created_at DESC
      `;
      const { rows } = await pool.query(query, [startDate, endDate]);
      return rows;
    } catch (error) {
      console.error('Error in getExpensesByDateRange:', error);
      throw error;
    }
  },

  // Get expenses for a specific month and year
  getExpensesByMonth: async (year, month, categoryId = null) => {
    try {
      // Get regular (non-recurring template) expenses for the specific month
      let expenseQuery = `
        SELECT e.*, ec.name as category_name, ec.color as category_color
        FROM expenses e
        JOIN expense_categories ec ON e.category_id = ec.id
        WHERE EXTRACT(YEAR FROM e.expense_date) = $1
        AND EXTRACT(MONTH FROM e.expense_date) = $2
        AND NOT (e.is_recurring = true AND e.recurring_template_id IS NULL)
      `;
      const expenseValues = [year, month];

      if (categoryId) {
        expenseQuery += ` AND e.category_id = $3`;
        expenseValues.push(categoryId);
      }

      expenseQuery += ` ORDER BY e.expense_date DESC, e.created_at DESC`;

      const { rows: monthlyExpenses } = await pool.query(expenseQuery, expenseValues);

      // Get all recurring expense templates that are NOT excluded for this month
      let recurringQuery = `
        SELECT e.*, ec.name as category_name, ec.color as category_color
        FROM expenses e
        JOIN expense_categories ec ON e.category_id = ec.id
        LEFT JOIN recurring_expense_exclusions ree ON (
          ree.recurring_template_id = e.id
          AND ree.year = $1
          AND ree.month = $2
        )
        WHERE e.is_recurring = true
        AND e.recurring_template_id IS NULL
        AND ree.id IS NULL
      `;
      const recurringValues = [year, month];

      if (categoryId) {
        recurringQuery += ` AND e.category_id = $3`;
        recurringValues.push(categoryId);
      }

      recurringQuery += ` ORDER BY e.name`;

      const { rows: recurringTemplates } = await pool.query(recurringQuery, recurringValues);

      // Create final expenses array
      const finalExpenses = [];

      // Add monthly expenses (mark auto-generated ones as recurring)
      monthlyExpenses.forEach(expense => {
        if (expense.recurring_template_id) {
          // This is an auto-generated recurring expense
          finalExpenses.push({
            ...expense,
            is_recurring: true
          });
        } else {
          // This is a regular expense
          finalExpenses.push(expense);
        }
      });

      // ALWAYS add recurring templates to ensure they're included in monthly totals
      // This ensures expenses like rent are always counted in every month
      const addedTemplateIds = new Set();

      // First, track which templates already have auto-generated instances
      monthlyExpenses.forEach(expense => {
        if (expense.recurring_template_id) {
          addedTemplateIds.add(expense.recurring_template_id);
        }
      });

      // Add ALL recurring templates - they should appear in every month
      // If there's an auto-generated instance, we'll show that instead of the template
      // If there's no auto-generated instance, we'll show the template with current month date
      recurringTemplates.forEach(template => {
        if (!addedTemplateIds.has(template.id)) {
          // No auto-generated instance exists, so include the template
          finalExpenses.push({
            ...template,
            is_recurring: true,
            // Use current month date for display
            expense_date: `${year}-${month.toString().padStart(2, '0')}-01`
          });
        }
        // If auto-generated instance exists, it's already added above
      });

      // Sort by date
      finalExpenses.sort((a, b) => {
        const dateA = new Date(a.expense_date);
        const dateB = new Date(b.expense_date);
        return dateB - dateA;
      });

      return finalExpenses;
    } catch (error) {
      console.error('Error in getExpensesByMonth:', error);
      throw error;
    }
  },

  // Get recurring expenses (templates)
  getRecurringExpenses: async () => {
    try {
      const query = `
        SELECT e.*, ec.name as category_name, ec.color as category_color
        FROM expenses e
        JOIN expense_categories ec ON e.category_id = ec.id
        WHERE e.is_recurring = true AND e.recurring_template_id IS NULL
        ORDER BY e.name
      `;
      const { rows } = await pool.query(query);
      return rows;
    } catch (error) {
      console.error('Error in getRecurringExpenses:', error);
      throw error;
    }
  },

  // Create recurring expenses for a new month
  createRecurringExpensesForMonth: async (year, month) => {
    try {
      // Get all recurring expense templates
      const recurringExpenses = await expenseModel.getRecurringExpenses();

      if (recurringExpenses.length === 0) {
        return [];
      }

      // Check if expenses already exist for this month
      const firstDay = new Date(year, month - 1, 1);
      const lastDay = new Date(year, month, 0);

      const existingExpenses = await expenseModel.getExpensesByDateRange(
        firstDay.toISOString().split('T')[0],
        lastDay.toISOString().split('T')[0]
      );

      const createdExpenses = [];

      for (const template of recurringExpenses) {
        // Check if this recurring expense already exists for this month
        const alreadyExists = existingExpenses.some(expense =>
          expense.recurring_template_id === template.id
        );

        if (!alreadyExists) {
          // Create new expense for this month (set to first day of month)
          const newExpense = await expenseModel.createExpense({
            category_id: template.category_id,
            name: template.name,
            amount: template.amount,
            description: template.description,
            expense_date: firstDay.toISOString().split('T')[0],
            is_recurring: false, // This is an instance, not a template
            recurring_template_id: template.id
          });

          createdExpenses.push(newExpense);
        }
      }

      return createdExpenses;
    } catch (error) {
      console.error('Error in createRecurringExpensesForMonth:', error);
      throw error;
    }
  },

  // Add exclusion for a recurring expense in a specific month
  addRecurringExpenseExclusion: async (recurringTemplateId, year, month) => {
    try {
      const query = `
        INSERT INTO recurring_expense_exclusions (recurring_template_id, year, month)
        VALUES ($1, $2, $3)
        ON CONFLICT (recurring_template_id, year, month) DO NOTHING
        RETURNING *
      `;
      const { rows } = await pool.query(query, [recurringTemplateId, year, month]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in addRecurringExpenseExclusion:', error);
      throw error;
    }
  },

  // Remove exclusion for a recurring expense in a specific month
  removeRecurringExpenseExclusion: async (recurringTemplateId, year, month) => {
    try {
      const query = `
        DELETE FROM recurring_expense_exclusions
        WHERE recurring_template_id = $1 AND year = $2 AND month = $3
        RETURNING *
      `;
      const { rows } = await pool.query(query, [recurringTemplateId, year, month]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in removeRecurringExpenseExclusion:', error);
      throw error;
    }
  },

  // Delete expense with month isolation logic
  deleteExpenseWithIsolation: async (id, year, month) => {
    try {
      // First, get the expense to check if it's recurring-related
      const expenseQuery = 'SELECT * FROM expenses WHERE id = $1';
      const { rows: expenseRows } = await pool.query(expenseQuery, [id]);

      if (expenseRows.length === 0) {
        return false;
      }

      const expense = expenseRows[0];

      // If this is a recurring template being deleted from a specific month
      if (expense.is_recurring && !expense.recurring_template_id && year && month) {
        // Don't delete the template, just add an exclusion for this month
        await expenseModel.addRecurringExpenseExclusion(expense.id, year, month);
        return true;
      }

      // If this is a monthly instance of a recurring expense
      if (expense.recurring_template_id && year && month) {
        // Delete the instance and add exclusion to prevent template from showing
        await pool.query('DELETE FROM expenses WHERE id = $1', [id]);
        await expenseModel.addRecurringExpenseExclusion(expense.recurring_template_id, year, month);
        return true;
      }

      // For regular expenses or global template deletion, use normal deletion
      const query = 'DELETE FROM expenses WHERE id = $1 RETURNING *';
      const { rows } = await pool.query(query, [id]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteExpenseWithIsolation:', error);
      throw error;
    }
  }
};

module.exports = expenseModel;
