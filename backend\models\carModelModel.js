// models/carModelModel.js
const pool = require('../database');

const carModelModel = {
  createModel: async (model) => {
    const { model_id, name, brand_id } = model;
    const query = 'INSERT INTO car_models (model_id, name, brand_id) VALUES ($1, $2, $3) RETURNING *';
    const values = [model_id, name, brand_id];
    
    try {
      const { rows } = await pool.query(query, values);
      return rows[0];
    } catch (error) {
      console.error('Error in createModel:', error);
      throw error;
    }
  },

  getAllModels: async () => {
    try {
      const query = 'SELECT * FROM car_models ORDER BY name';
      const { rows } = await pool.query(query);
      return rows;
    } catch (error) {
      console.error('Error in getAllModels:', error);
      throw error;
    }
  },

  getModelsByBrand: async (brand_id) => {
    try {
      const query = 'SELECT * FROM car_models WHERE brand_id = $1 ORDER BY name';
      const { rows } = await pool.query(query, [brand_id]);
      return rows;
    } catch (error) {
      console.error('Error in getModelsByBrand:', error);
      throw error;
    }
  },

  getModelById: async (model_id) => {
    try {
      const query = 'SELECT * FROM car_models WHERE model_id = $1';
      const { rows } = await pool.query(query, [model_id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in getModelById:', error);
      throw error;
    }
  },

  updateModel: async (model_id, name, brand_id) => {
    try {
      const query = 'UPDATE car_models SET name = $1, brand_id = $2 WHERE model_id = $3 RETURNING *';
      const { rows } = await pool.query(query, [name, brand_id, model_id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in updateModel:', error);
      throw error;
    }
  },

  deleteModel: async (model_id) => {
    try {
      const query = 'DELETE FROM car_models WHERE model_id = $1 RETURNING *';
      const { rows } = await pool.query(query, [model_id]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteModel:', error);
      throw error;
    }
  },

  // Check if any products are using this model
  isModelInUse: async (model_id) => {
    try {
      const query = 'SELECT COUNT(*) FROM products WHERE car_model = $1';
      const { rows } = await pool.query(query, [model_id]);
      return parseInt(rows[0].count) > 0;
    } catch (error) {
      console.error('Error in isModelInUse:', error);
      throw error;
    }
  }
};

module.exports = carModelModel;
