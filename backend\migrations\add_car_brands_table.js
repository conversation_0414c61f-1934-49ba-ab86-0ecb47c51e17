const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
const { Pool } = require('pg');

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
});

async function addCarBrandsTable() {
  const client = await pool.connect();
  try {
    console.log('Starting migration: Adding car_brands table...');
    
    // Check if the table already exists
    const checkTableQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'car_brands';
    `;
    
    const { rows } = await client.query(checkTableQuery);
    
    if (rows.length === 0) {
      // Table doesn't exist, so create it
      const createTableQuery = `
        CREATE TABLE car_brands (
          id SERIAL PRIMARY KEY,
          brand_id TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `;
      
      await client.query(createTableQuery);
      console.log('Migration successful: car_brands table created.');
      
      // Insert default car brands
      const defaultBrands = [
        { brand_id: 'toyota', name: 'Toyota' },
        { brand_id: 'honda', name: 'Honda' },
        { brand_id: 'ford', name: 'Ford' },
        { brand_id: 'chevrolet', name: 'Chevrolet' },
        { brand_id: 'bmw', name: 'BMW' },
        { brand_id: 'mercedes_benz', name: 'Mercedes-Benz' },
        { brand_id: 'audi', name: 'Audi' },
        { brand_id: 'volkswagen', name: 'Volkswagen' },
        { brand_id: 'nissan', name: 'Nissan' },
        { brand_id: 'hyundai', name: 'Hyundai' },
        { brand_id: 'kia', name: 'Kia' },
        { brand_id: 'mazda', name: 'Mazda' },
        { brand_id: 'subaru', name: 'Subaru' },
        { brand_id: 'lexus', name: 'Lexus' },
        { brand_id: 'jeep', name: 'Jeep' },
        { brand_id: 'tesla', name: 'Tesla' }
      ];
      
      for (const brand of defaultBrands) {
        const insertQuery = `
          INSERT INTO car_brands (brand_id, name)
          VALUES ($1, $2)
          ON CONFLICT (brand_id) DO NOTHING;
        `;
        await client.query(insertQuery, [brand.brand_id, brand.name]);
      }
      
      console.log('Default car brands inserted successfully.');
    } else {
      console.log('Table car_brands already exists. No changes made.');
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration
addCarBrandsTable()
  .then(() => {
    console.log('Migration completed.');
    // Add a small delay before exiting to ensure all DB operations complete
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
