// Migration to add company_name column to sales table
require('dotenv').config();
const { Pool } = require('pg');

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
});

async function addCompanyNameToSales() {
  const client = await pool.connect();
  try {
    console.log('Adding company_name column to sales table...');
    
    // Check if the column already exists
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'sales' AND column_name = 'company_name';
    `;
    
    const { rows } = await client.query(checkColumnQuery);
    
    if (rows.length === 0) {
      // Add the company_name column to the sales table
      await client.query(`
        ALTER TABLE sales 
        ADD COLUMN company_name TEXT DEFAULT 'Shans Accessories PTY LTD';
      `);
      console.log('Successfully added company_name column to sales table.');
    } else {
      console.log('company_name column already exists in sales table.');
    }
    
  } catch (error) {
    console.error('Error adding company_name column to sales table:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the function
addCompanyNameToSales()
  .then(() => {
    console.log('Migration completed successfully.');
    // Add a small delay before exiting to ensure all DB operations complete
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
