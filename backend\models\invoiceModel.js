// models/invoiceModel.js
const pool = require('../database');

const invoiceModel = {
  createInvoice: async (invoiceData) => {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Insert invoice record
      const invoiceQuery = `
        INSERT INTO invoices (
          reference_number, date, billing_name, billing_address,
          billing_email, billing_phone, shipping_name, shipping_address,
          shipping_email, shipping_phone, payment_method, subtotal,
          tax, total, salesperson_name, company_name, comments
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        RETURNING id
      `;

      const invoiceValues = [
        invoiceData.reference_number,
        invoiceData.date,
        invoiceData.billing_name,
        invoiceData.billing_address,
        invoiceData.billing_email,
        invoiceData.billing_phone,
        invoiceData.shipping_name,
        invoiceData.shipping_address,
        invoiceData.shipping_email,
        invoiceData.shipping_phone,
        invoiceData.payment_method,
        invoiceData.subtotal,
        invoiceData.tax,
        invoiceData.total,
        invoiceData.salesperson_name || '',
        invoiceData.company_name || 'Shans Accessories PTY LTD',
        invoiceData.comments || ''
      ];

      const { rows } = await client.query(invoiceQuery, invoiceValues);
      const invoiceId = rows[0].id;

      // Insert invoice items
      if (invoiceData.products && invoiceData.products.length > 0) {
        const itemQuery = `
          INSERT INTO invoice_items (
            invoice_id, item_code, item_name, quantity,
            unit_price_excluding_tax, tax_per_product, total_price
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `;

        for (const product of invoiceData.products) {
          await client.query(itemQuery, [
            invoiceId,
            product.item_code || '',
            product.name || product.item_name || '',
            product.quantity || 1,
            product.unitPriceExcludingTax || 0,
            product.taxPerUnit || 0,
            product.totalPrice || 0
          ]);
        }
      }

      await client.query('COMMIT');
      return invoiceId;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Error in createInvoice:', error);
      throw error;
    } finally {
      client.release();
    }
  },

  getAllInvoices: async () => {
    try {
      const query = `
        SELECT
          i.id AS invoice_id,
          i.reference_number,
          i.date,
          i.billing_name,
          i.billing_address,
          i.billing_email,
          i.billing_phone,
          i.shipping_name,
          i.shipping_address,
          i.shipping_email,
          i.shipping_phone,
          i.payment_method,
          i.subtotal,
          i.tax,
          i.total,
          i.salesperson_name,
          i.company_name,
          i.comments,
          i.created_at,
          ii.item_code,
          ii.item_name,
          ii.quantity,
          ii.unit_price_excluding_tax,
          ii.tax_per_product,
          ii.total_price
        FROM invoices i
        LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
        ORDER BY i.date DESC, i.id DESC
      `;

      const { rows } = await pool.query(query);

      // Group the results by invoice
      const invoicesMap = new Map();

      rows.forEach(row => {
        const invoiceId = row.invoice_id;

        if (!invoicesMap.has(invoiceId)) {
          invoicesMap.set(invoiceId, {
            invoice_id: invoiceId,
            reference_number: row.reference_number,
            date: row.date,
            billing_name: row.billing_name,
            billing_address: row.billing_address,
            billing_email: row.billing_email,
            billing_phone: row.billing_phone,
            shipping_name: row.shipping_name,
            shipping_address: row.shipping_address,
            shipping_email: row.shipping_email,
            shipping_phone: row.shipping_phone,
            payment_method: row.payment_method,
            subtotal: row.subtotal,
            tax: row.tax,
            total: row.total,
            salesperson_name: row.salesperson_name,
            company_name: row.company_name || 'Shans Accessories PTY LTD',
            comments: row.comments,
            created_at: row.created_at,
            items: []
          });
        }

        // Add item if it exists
        if (row.item_code) {
          invoicesMap.get(invoiceId).items.push({
            item_code: row.item_code,
            item_name: row.item_name,
            quantity: row.quantity,
            unit_price_excluding_tax: row.unit_price_excluding_tax,
            tax_per_product: row.tax_per_product,
            total_price: row.total_price
          });
        }
      });

      return Array.from(invoicesMap.values());
    } catch (error) {
      console.error('Error in getAllInvoices:', error);
      throw error;
    }
  },

  getInvoiceById: async (invoiceId) => {
    try {
      const query = `
        SELECT
          i.id AS invoice_id,
          i.reference_number,
          i.date,
          i.billing_name,
          i.billing_address,
          i.billing_email,
          i.billing_phone,
          i.shipping_name,
          i.shipping_address,
          i.shipping_email,
          i.shipping_phone,
          i.payment_method,
          i.subtotal,
          i.tax,
          i.total,
          i.salesperson_name,
          i.company_name,
          i.comments,
          i.created_at,
          ii.item_code,
          ii.item_name,
          ii.quantity,
          ii.unit_price_excluding_tax,
          ii.tax_per_product,
          ii.total_price
        FROM invoices i
        LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
        WHERE i.id = $1
      `;

      const { rows } = await pool.query(query, [invoiceId]);

      if (rows.length === 0) {
        return null;
      }

      // Construct the invoice object with items
      const invoice = {
        invoice_id: rows[0].invoice_id,
        reference_number: rows[0].reference_number,
        date: rows[0].date,
        billing_name: rows[0].billing_name,
        billing_address: rows[0].billing_address,
        billing_email: rows[0].billing_email,
        billing_phone: rows[0].billing_phone,
        shipping_name: rows[0].shipping_name,
        shipping_address: rows[0].shipping_address,
        shipping_email: rows[0].shipping_email,
        shipping_phone: rows[0].shipping_phone,
        payment_method: rows[0].payment_method,
        subtotal: rows[0].subtotal,
        tax: rows[0].tax,
        total: rows[0].total,
        salesperson_name: rows[0].salesperson_name,
        company_name: rows[0].company_name || 'Shans Accessories PTY LTD',
        comments: rows[0].comments,
        created_at: rows[0].created_at,
        items: []
      };

      // Add items
      rows.forEach(row => {
        if (row.item_code) {
          invoice.items.push({
            item_code: row.item_code,
            item_name: row.item_name,
            quantity: row.quantity,
            unit_price_excluding_tax: row.unit_price_excluding_tax,
            tax_per_product: row.tax_per_product,
            total_price: row.total_price
          });
        }
      });

      return invoice;
    } catch (error) {
      console.error('Error in getInvoiceById:', error);
      throw error;
    }
  },

  deleteInvoice: async (invoiceId) => {
    try {
      const query = 'DELETE FROM invoices WHERE id = $1 RETURNING id';
      const { rows } = await pool.query(query, [invoiceId]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteInvoice:', error);
      throw error;
    }
  }
};

module.exports = invoiceModel;
