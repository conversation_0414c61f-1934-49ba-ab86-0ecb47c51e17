const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
const { Pool } = require('pg');

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
});

async function addCategoriesTable() {
  const client = await pool.connect();
  try {
    console.log('Starting migration: Adding categories table...');

    // Check if the table already exists
    const checkTableQuery = `
      SELECT table_name
      FROM information_schema.tables
      WHERE table_name = 'categories';
    `;

    const { rows } = await client.query(checkTableQuery);

    if (rows.length === 0) {
      // Table doesn't exist, so create it
      const createTableQuery = `
        CREATE TABLE categories (
          id SERIAL PRIMARY KEY,
          category_id TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `;

      await client.query(createTableQuery);
      console.log('Migration successful: categories table created.');

      // Insert default categories
      const defaultCategories = [
        { category_id: 'lips', name: 'Lips' },
        { category_id: 'boot_spoilers', name: 'Boot spoilers' },
        { category_id: 'roof_spoilers', name: 'Roof spoilers' },
        { category_id: 'diffusers', name: 'Diffusers' },
        { category_id: 'sills_extensions', name: 'Sills extensions' },
        { category_id: 'eyelids', name: 'Eyelids' },
        { category_id: 'mirrors_covers', name: 'Mirrors covers' },
        { category_id: 'grills', name: 'Grills' },
        { category_id: 'canards', name: 'Canards' },
        { category_id: 'bumper_spats', name: 'Bumper spats' },
        { category_id: 'arches', name: 'Arches' },
        { category_id: 'step_bars', name: 'Step bars' },
        { category_id: 'bonnet_scoops', name: 'Bonnet scoops' },
        { category_id: 'roll_bars', name: 'Roll bars' },
        { category_id: 'fog_grills', name: 'Fog grills' },
        { category_id: 'vinyl', name: 'Vinyl' }
      ];

      for (const category of defaultCategories) {
        const insertQuery = `
          INSERT INTO categories (category_id, name)
          VALUES ($1, $2)
          ON CONFLICT (category_id) DO NOTHING;
        `;
        await client.query(insertQuery, [category.category_id, category.name]);
      }

      console.log('Default categories inserted successfully.');
    } else {
      console.log('Table categories already exists. No changes made.');
    }

  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration
addCategoriesTable()
  .then(() => {
    console.log('Migration completed.');
    // Add a small delay before exiting to ensure all DB operations complete
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
