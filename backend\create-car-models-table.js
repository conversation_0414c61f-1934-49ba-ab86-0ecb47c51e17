// Manual script to create the car_models table
require('dotenv').config();
const { Pool } = require('pg');

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
});

async function createCarModelsTable() {
  const client = await pool.connect();
  try {
    console.log('Creating car_models table...');
    
    // Create the car_models table
    await client.query(`
      CREATE TABLE IF NOT EXISTS car_models (
        id SERIAL PRIMARY KEY,
        model_id TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        brand_id TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    console.log('Car models table created successfully.');
    
    // Insert default car models
    const defaultModels = [
      { model_id: 'corolla', name: '<PERSON><PERSON><PERSON>', brand_id: 'toyota' },
      { model_id: 'camry', name: '<PERSON><PERSON>', brand_id: 'toyota' },
      { model_id: 'hilux', name: 'Hilux', brand_id: 'toyota' },
      { model_id: 'civic', name: 'Civic', brand_id: 'honda' },
      { model_id: 'accord', name: 'Accord', brand_id: 'honda' },
      { model_id: 'mustang', name: 'Mustang', brand_id: 'ford' },
      { model_id: 'f150', name: 'F-150', brand_id: 'ford' },
      { model_id: 'silverado', name: 'Silverado', brand_id: 'chevrolet' },
      { model_id: 'cruze', name: 'Cruze', brand_id: 'chevrolet' },
      { model_id: '3_series', name: '3 Series', brand_id: 'bmw' },
      { model_id: '5_series', name: '5 Series', brand_id: 'bmw' },
      { model_id: 'c_class', name: 'C-Class', brand_id: 'mercedes_benz' },
      { model_id: 'e_class', name: 'E-Class', brand_id: 'mercedes_benz' },
      { model_id: 'a4', name: 'A4', brand_id: 'audi' },
      { model_id: 'q5', name: 'Q5', brand_id: 'audi' },
      { model_id: 'golf', name: 'Golf', brand_id: 'volkswagen' },
      { model_id: 'polo', name: 'Polo', brand_id: 'volkswagen' },
      { model_id: 'altima', name: 'Altima', brand_id: 'nissan' },
      { model_id: 'sentra', name: 'Sentra', brand_id: 'nissan' },
      { model_id: 'elantra', name: 'Elantra', brand_id: 'hyundai' },
      { model_id: 'tucson', name: 'Tucson', brand_id: 'hyundai' },
      { model_id: 'sportage', name: 'Sportage', brand_id: 'kia' },
      { model_id: 'sorento', name: 'Sorento', brand_id: 'kia' },
      { model_id: 'mazda3', name: 'Mazda3', brand_id: 'mazda' },
      { model_id: 'cx5', name: 'CX-5', brand_id: 'mazda' },
      { model_id: 'impreza', name: 'Impreza', brand_id: 'subaru' },
      { model_id: 'forester', name: 'Forester', brand_id: 'subaru' },
      { model_id: 'rx', name: 'RX', brand_id: 'lexus' },
      { model_id: 'es', name: 'ES', brand_id: 'lexus' },
      { model_id: 'wrangler', name: 'Wrangler', brand_id: 'jeep' },
      { model_id: 'cherokee', name: 'Cherokee', brand_id: 'jeep' },
      { model_id: 'model_3', name: 'Model 3', brand_id: 'tesla' },
      { model_id: 'model_y', name: 'Model Y', brand_id: 'tesla' }
    ];
    
    console.log('Inserting default car models...');
    
    for (const model of defaultModels) {
      await client.query(`
        INSERT INTO car_models (model_id, name, brand_id)
        VALUES ($1, $2, $3)
        ON CONFLICT (model_id) DO NOTHING;
      `, [model.model_id, model.name, model.brand_id]);
    }
    
    console.log('Default car models inserted successfully.');
    
  } catch (error) {
    console.error('Error creating car_models table:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the function
createCarModelsTable()
  .then(() => {
    console.log('Car models table setup completed.');
    // Add a small delay before exiting to ensure all DB operations complete
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  })
  .catch((error) => {
    console.error('Failed to set up car models table:', error);
    process.exit(1);
  });
