// Manual script to create the categories table
require('dotenv').config();
const { Pool } = require('pg');

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
});

async function createCategoriesTable() {
  const client = await pool.connect();
  try {
    console.log('Creating categories table...');
    
    // Create the categories table
    await client.query(`
      CREATE TABLE IF NOT EXISTS categories (
        id SERIAL PRIMARY KEY,
        category_id TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    console.log('Categories table created successfully.');
    
    // Insert default categories
    const defaultCategories = [
      { category_id: 'lips', name: 'Lips' },
      { category_id: 'boot_spoilers', name: 'Boot spoilers' },
      { category_id: 'roof_spoilers', name: 'Roof spoilers' },
      { category_id: 'diffusers', name: 'Diffusers' },
      { category_id: 'sills_extensions', name: 'Sills extensions' },
      { category_id: 'eyelids', name: 'Eyelids' },
      { category_id: 'mirrors_covers', name: 'Mirrors covers' },
      { category_id: 'grills', name: 'Grills' },
      { category_id: 'canards', name: 'Canards' },
      { category_id: 'bumper_spats', name: 'Bumper spats' },
      { category_id: 'arches', name: 'Arches' },
      { category_id: 'step_bars', name: 'Step bars' },
      { category_id: 'bonnet_scoops', name: 'Bonnet scoops' },
      { category_id: 'roll_bars', name: 'Roll bars' },
      { category_id: 'fog_grills', name: 'Fog grills' },
      { category_id: 'vinyl', name: 'Vinyl' }
    ];
    
    console.log('Inserting default categories...');
    
    for (const category of defaultCategories) {
      await client.query(`
        INSERT INTO categories (category_id, name)
        VALUES ($1, $2)
        ON CONFLICT (category_id) DO NOTHING;
      `, [category.category_id, category.name]);
    }
    
    console.log('Default categories inserted successfully.');
    
  } catch (error) {
    console.error('Error creating categories table:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the function
createCategoriesTable()
  .then(() => {
    console.log('Categories table setup completed.');
    // Add a small delay before exiting to ensure all DB operations complete
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  })
  .catch((error) => {
    console.error('Failed to set up categories table:', error);
    process.exit(1);
  });
