// Backup Current Database Script
// Creates a backup of the current database before migration

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Current database connection (Neon)
const dbConfig = {
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
};

const pool = new Pool(dbConfig);

const TABLES_TO_BACKUP = [
  'users',
  'rooms',
  'expense_categories',
  'categories',
  'car_brands',
  'car_models',
  'products',
  'sales',
  'sale_items',
  'expenses'
];

async function getAllTables() {
  const query = `
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
    ORDER BY table_name
  `;
  
  const result = await pool.query(query);
  return result.rows.map(row => row.table_name);
}

async function getTableData(tableName) {
  try {
    const result = await pool.query(`SELECT * FROM ${tableName}`);
    return result.rows;
  } catch (error) {
    console.error(`Error fetching data from ${tableName}:`, error.message);
    return [];
  }
}

async function getTableSchema(tableName) {
  const query = `
    SELECT 
      column_name, 
      data_type, 
      is_nullable, 
      column_default,
      character_maximum_length
    FROM information_schema.columns
    WHERE table_name = $1
    ORDER BY ordinal_position
  `;
  
  const result = await pool.query(query, [tableName]);
  return result.rows;
}

async function createBackup() {
  console.log('🔄 Starting database backup...');
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(__dirname, 'backups');
  const backupFile = path.join(backupDir, `database-backup-${timestamp}.json`);
  
  // Create backups directory if it doesn't exist
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  try {
    // Test connection
    const client = await pool.connect();
    const result = await client.query('SELECT NOW() as time');
    console.log('✅ Connected to database at:', result.rows[0].time);
    client.release();
    
    // Get all tables
    const allTables = await getAllTables();
    console.log(`📊 Found ${allTables.length} tables:`, allTables.join(', '));
    
    const backup = {
      timestamp: new Date().toISOString(),
      database: 'neondb',
      tables: {}
    };
    
    let totalRecords = 0;
    
    // Backup each table
    for (const tableName of allTables) {
      console.log(`📦 Backing up table: ${tableName}`);
      
      const schema = await getTableSchema(tableName);
      const data = await getTableData(tableName);
      
      backup.tables[tableName] = {
        schema: schema,
        data: data,
        recordCount: data.length
      };
      
      totalRecords += data.length;
      console.log(`✅ ${tableName}: ${data.length} records`);
    }
    
    // Write backup to file
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    console.log('');
    console.log('🎉 Backup completed successfully!');
    console.log(`📁 Backup file: ${backupFile}`);
    console.log(`📊 Total tables: ${allTables.length}`);
    console.log(`📊 Total records: ${totalRecords}`);
    console.log(`💾 File size: ${(fs.statSync(backupFile).size / 1024 / 1024).toFixed(2)} MB`);
    
    return backupFile;
    
  } catch (error) {
    console.error('💥 Backup failed:', error.message);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run backup if this file is executed directly
if (require.main === module) {
  createBackup()
    .then(backupFile => {
      console.log('\n✅ Backup process completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Backup process failed:', error.message);
      process.exit(1);
    });
}

module.exports = { createBackup };
