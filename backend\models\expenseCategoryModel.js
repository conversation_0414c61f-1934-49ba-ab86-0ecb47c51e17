// models/expenseCategoryModel.js
const pool = require('../database');

const expenseCategoryModel = {
  createCategory: async (category) => {
    const { name, description, color } = category;
    const query = 'INSERT INTO expense_categories (name, description, color) VALUES ($1, $2, $3) RETURNING *';
    const values = [name, description, color || '#3498db'];
    
    try {
      const { rows } = await pool.query(query, values);
      return rows[0];
    } catch (error) {
      console.error('Error in createCategory:', error);
      throw error;
    }
  },

  getAllCategories: async () => {
    try {
      const query = 'SELECT * FROM expense_categories ORDER BY name';
      const { rows } = await pool.query(query);
      return rows;
    } catch (error) {
      console.error('Error in getAllCategories:', error);
      throw error;
    }
  },

  getCategoryById: async (id) => {
    try {
      const query = 'SELECT * FROM expense_categories WHERE id = $1';
      const { rows } = await pool.query(query, [id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in getCategoryById:', error);
      throw error;
    }
  },

  updateCategory: async (id, category) => {
    const { name, description, color } = category;
    try {
      const query = 'UPDATE expense_categories SET name = $1, description = $2, color = $3 WHERE id = $4 RETURNING *';
      const { rows } = await pool.query(query, [name, description, color, id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in updateCategory:', error);
      throw error;
    }
  },

  deleteCategory: async (id) => {
    try {
      const query = 'DELETE FROM expense_categories WHERE id = $1 RETURNING *';
      const { rows } = await pool.query(query, [id]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteCategory:', error);
      throw error;
    }
  },

  // Check if any expenses are using this category
  isCategoryInUse: async (id) => {
    try {
      const query = 'SELECT COUNT(*) FROM expenses WHERE category_id = $1';
      const { rows } = await pool.query(query, [id]);
      return parseInt(rows[0].count) > 0;
    } catch (error) {
      console.error('Error in isCategoryInUse:', error);
      throw error;
    }
  }
};

module.exports = expenseCategoryModel;
