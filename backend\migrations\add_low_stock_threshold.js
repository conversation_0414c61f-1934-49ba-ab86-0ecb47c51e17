// Migration to add low_stock_threshold column to products table
require('dotenv').config();
const { Pool } = require('pg');

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
});

async function addLowStockThresholdColumn() {
  const client = await pool.connect();
  try {
    console.log('Starting migration: Adding low_stock_threshold column to products table...');
    
    // Check if the column already exists
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products' AND column_name = 'low_stock_threshold';
    `;
    
    const { rows } = await client.query(checkColumnQuery);
    
    if (rows.length === 0) {
      // Column doesn't exist, so add it
      const addColumnQuery = `
        ALTER TABLE products 
        ADD COLUMN low_stock_threshold INTEGER DEFAULT 5;
      `;
      
      await client.query(addColumnQuery);
      console.log('Migration successful: low_stock_threshold column added to products table.');
    } else {
      console.log('Column low_stock_threshold already exists in products table. No changes made.');
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration
addLowStockThresholdColumn()
  .then(() => {
    console.log('Migration completed.');
    // Add a small delay before exiting to ensure all DB operations complete
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
