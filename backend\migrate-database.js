// Database Migration Script
// Migrates data from old Neon database to new Render PostgreSQL database

const { Pool } = require('pg');
require('dotenv').config();

// Old database connection (Neon)
const oldDbConfig = {
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
};

// New database connection (Render)
const newDbConfig = {
  connectionString: 'postgresql://shansdb_user:<EMAIL>/shansdb?sslmode=require',
  ssl: { rejectUnauthorized: false }
};

const oldPool = new Pool(oldDbConfig);
const newPool = new Pool(newDbConfig);

// Tables to migrate in order (respecting foreign key dependencies)
const TABLES_TO_MIGRATE = [
  'users',
  'rooms',
  'expense_categories',
  'categories',
  'car_brands',
  'car_models',
  'products',
  'sales',
  'sale_items',
  'expenses',
  'quotations',
  'quotation_items',
  'invoices',
  'invoice_items'
];

async function testConnections() {
  console.log('🔍 Testing database connections...');
  
  try {
    // Test old database
    const oldClient = await oldPool.connect();
    const oldResult = await oldClient.query('SELECT NOW() as time, version() as version');
    console.log('✅ Old database (Neon) connected:', oldResult.rows[0].time);
    oldClient.release();
    
    // Test new database
    const newClient = await newPool.connect();
    const newResult = await newClient.query('SELECT NOW() as time, version() as version');
    console.log('✅ New database (Render) connected:', newResult.rows[0].time);
    newClient.release();
    
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

async function getTableSchema(pool, tableName) {
  const query = `
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns
    WHERE table_name = $1
    ORDER BY ordinal_position
  `;
  
  try {
    const result = await pool.query(query, [tableName]);
    return result.rows;
  } catch (error) {
    console.log(`⚠️  Table ${tableName} does not exist in source database`);
    return [];
  }
}

async function tableExists(pool, tableName) {
  const query = `
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_name = $1
    )
  `;
  
  const result = await pool.query(query, [tableName]);
  return result.rows[0].exists;
}

async function getTableData(pool, tableName) {
  try {
    const result = await pool.query(`SELECT * FROM ${tableName}`);
    return result.rows;
  } catch (error) {
    console.log(`⚠️  Could not fetch data from ${tableName}:`, error.message);
    return [];
  }
}

async function createTablesInNewDatabase() {
  console.log('🏗️  Creating tables in new database...');
  
  const newClient = await newPool.connect();
  
  try {
    // Import the database initialization from our existing setup
    const { initializeTables } = require('./database');
    
    // Run the initialization which will create all tables
    await initializeTables();
    
    console.log('✅ Tables created successfully in new database');
  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    throw error;
  } finally {
    newClient.release();
  }
}

async function migrateTableData(tableName) {
  console.log(`📦 Migrating table: ${tableName}`);
  
  // Check if table exists in old database
  const oldTableExists = await tableExists(oldPool, tableName);
  if (!oldTableExists) {
    console.log(`⏭️  Table ${tableName} doesn't exist in old database, skipping...`);
    return { migrated: 0, skipped: true };
  }
  
  // Get data from old database
  const data = await getTableData(oldPool, tableName);
  
  if (data.length === 0) {
    console.log(`⏭️  Table ${tableName} is empty, skipping...`);
    return { migrated: 0, empty: true };
  }
  
  console.log(`📊 Found ${data.length} records in ${tableName}`);
  
  // Get table schema to build insert query
  const schema = await getTableSchema(oldPool, tableName);
  const columns = schema.map(col => col.column_name);
  
  if (columns.length === 0) {
    console.log(`⚠️  Could not get schema for ${tableName}, skipping...`);
    return { migrated: 0, error: 'No schema' };
  }
  
  const newClient = await newPool.connect();
  let migrated = 0;
  
  try {
    await newClient.query('BEGIN');
    
    // Clear existing data in new database for this table
    await newClient.query(`DELETE FROM ${tableName}`);
    console.log(`🗑️  Cleared existing data from ${tableName}`);
    
    // Insert data in batches for better performance
    const batchSize = 50;
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      console.log(`📦 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(data.length/batchSize)} for ${tableName}`);

      for (const row of batch) {
        const values = columns.map(col => row[col]);
        const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
        const insertQuery = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;

        try {
          await newClient.query(insertQuery, values);
          migrated++;
        } catch (error) {
          console.error(`❌ Error inserting row into ${tableName}:`, error.message);
          console.error('Row data:', row);
          // Continue with other rows
        }
      }

      // Small delay between batches to prevent overwhelming the database
      if (i + batchSize < data.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    await newClient.query('COMMIT');
    console.log(`✅ Migrated ${migrated}/${data.length} records to ${tableName}`);
    
  } catch (error) {
    await newClient.query('ROLLBACK');
    console.error(`❌ Error migrating ${tableName}:`, error.message);
    throw error;
  } finally {
    newClient.release();
  }
  
  return { migrated, total: data.length };
}

async function verifyMigration() {
  console.log('🔍 Verifying migration...');
  
  const verification = {};
  
  for (const tableName of TABLES_TO_MIGRATE) {
    try {
      // Count records in old database
      const oldExists = await tableExists(oldPool, tableName);
      let oldCount = 0;
      if (oldExists) {
        const oldResult = await oldPool.query(`SELECT COUNT(*) FROM ${tableName}`);
        oldCount = parseInt(oldResult.rows[0].count);
      }
      
      // Count records in new database
      const newExists = await tableExists(newPool, tableName);
      let newCount = 0;
      if (newExists) {
        const newResult = await newPool.query(`SELECT COUNT(*) FROM ${tableName}`);
        newCount = parseInt(newResult.rows[0].count);
      }
      
      verification[tableName] = { old: oldCount, new: newCount, match: oldCount === newCount };
      
      if (oldCount === newCount) {
        console.log(`✅ ${tableName}: ${oldCount} → ${newCount} records`);
      } else {
        console.log(`⚠️  ${tableName}: ${oldCount} → ${newCount} records (MISMATCH)`);
      }
      
    } catch (error) {
      console.error(`❌ Error verifying ${tableName}:`, error.message);
      verification[tableName] = { error: error.message };
    }
  }
  
  return verification;
}

async function runMigration() {
  console.log('🚀 Starting database migration...');
  console.log('📍 From: Neon PostgreSQL');
  console.log('📍 To: Render PostgreSQL');
  console.log('');
  
  try {
    // Test connections
    const connectionsOk = await testConnections();
    if (!connectionsOk) {
      throw new Error('Database connections failed');
    }
    
    console.log('');
    
    // Create tables in new database
    await createTablesInNewDatabase();
    
    console.log('');
    
    // Migrate data table by table
    const migrationResults = {};
    
    for (const tableName of TABLES_TO_MIGRATE) {
      try {
        const result = await migrateTableData(tableName);
        migrationResults[tableName] = result;
      } catch (error) {
        console.error(`❌ Failed to migrate ${tableName}:`, error.message);
        migrationResults[tableName] = { error: error.message };
      }
    }
    
    console.log('');
    
    // Verify migration
    const verification = await verifyMigration();
    
    console.log('');
    console.log('📊 Migration Summary:');
    console.log('====================');
    
    let totalMigrated = 0;
    let totalErrors = 0;
    
    for (const [tableName, result] of Object.entries(migrationResults)) {
      if (result.error) {
        console.log(`❌ ${tableName}: ERROR - ${result.error}`);
        totalErrors++;
      } else if (result.skipped) {
        console.log(`⏭️  ${tableName}: SKIPPED (table doesn't exist)`);
      } else if (result.empty) {
        console.log(`📭 ${tableName}: EMPTY`);
      } else {
        console.log(`✅ ${tableName}: ${result.migrated} records migrated`);
        totalMigrated += result.migrated;
      }
    }
    
    console.log('');
    console.log(`🎉 Migration completed!`);
    console.log(`📊 Total records migrated: ${totalMigrated}`);
    console.log(`❌ Tables with errors: ${totalErrors}`);
    
    if (totalErrors === 0) {
      console.log('');
      console.log('✅ All data migrated successfully!');
      console.log('🔄 The application is now configured to use the new database.');
      console.log('🗑️  You can safely remove the old database connection after testing.');
    }
    
  } catch (error) {
    console.error('💥 Migration failed:', error.message);
    process.exit(1);
  } finally {
    await oldPool.end();
    await newPool.end();
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration, testConnections, verifyMigration };
