const pool = require('../database');
const bcrypt = require('bcryptjs');

class UserModel {
  // Create users table if it doesn't exist
  static async initializeUsersTable() {
    const client = await pool.connect();
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          is_admin BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create default admin user if no users exist
      const userCount = await client.query('SELECT COUNT(*) as count FROM users');
      if (parseInt(userCount.rows[0].count) === 0) {
        const hashedPassword = await bcrypt.hash('admin123', 10);
        await client.query(
          'INSERT INTO users (email, password_hash, is_admin) VALUES ($1, $2, $3)',
          ['<EMAIL>', hashedPassword, true]
        );
        console.log('Default admin user created: <EMAIL>');
      }
    } finally {
      client.release();
    }
  }

  // Create a new user
  static async createUser(email, password, isAdmin = false) {
    const client = await pool.connect();
    try {
      const hashedPassword = await bcrypt.hash(password, 10);
      const result = await client.query(
        'INSERT INTO users (email, password_hash, is_admin) VALUES ($1, $2, $3) RETURNING id, email, is_admin, created_at',
        [email, hashedPassword, isAdmin]
      );
      return result.rows[0];
    } finally {
      client.release();
    }
  }

  // Get user by email
  static async getUserByEmail(email) {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT id, email, password_hash, is_admin, created_at FROM users WHERE email = $1',
        [email]
      );
      const user = result.rows[0];
      if (user) {
        user.is_admin = Boolean(user.is_admin);
      }
      return user;
    } finally {
      client.release();
    }
  }

  // Get user by ID
  static async getUserById(id) {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT id, email, is_admin, created_at FROM users WHERE id = $1',
        [id]
      );
      const user = result.rows[0];
      if (user) {
        user.is_admin = Boolean(user.is_admin);
      }
      return user;
    } finally {
      client.release();
    }
  }

  // Get all users
  static async getAllUsers() {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT id, email, is_admin, created_at FROM users ORDER BY created_at DESC'
      );
      return result.rows.map(user => ({
        ...user,
        is_admin: Boolean(user.is_admin)
      }));
    } finally {
      client.release();
    }
  }

  // Update user
  static async updateUser(id, email, isAdmin) {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'UPDATE users SET email = $1, is_admin = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3 RETURNING id, email, is_admin, updated_at',
        [email, isAdmin, id]
      );
      const user = result.rows[0];
      if (user) {
        user.is_admin = Boolean(user.is_admin);
      }
      return user;
    } finally {
      client.release();
    }
  }

  // Update user password
  static async updateUserPassword(id, newPassword) {
    const client = await pool.connect();
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      await client.query(
        'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [hashedPassword, id]
      );
      return true;
    } finally {
      client.release();
    }
  }

  // Delete user
  static async deleteUser(id) {
    const client = await pool.connect();
    try {
      const result = await client.query('DELETE FROM users WHERE id = $1', [id]);
      return result.rowCount > 0;
    } finally {
      client.release();
    }
  }

  // Verify password
  static async verifyPassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  // Check if user is admin
  static async isAdmin(userId) {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT is_admin FROM users WHERE id = ?',
        [userId]
      );
      return Boolean(result.rows[0]?.is_admin) || false;
    } finally {
      client.release();
    }
  }
}

module.exports = UserModel;
