// backend/routes/expenseMediaRoutes.js
const express = require('express');
const { v2: cloudinary } = require('cloudinary');
const multer = require('multer');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const openaiService = require('../services/openaiService');

const router = express.Router();

// Configure multer for file uploads (store in memory)
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        // Accept images and PDFs
        const allowedTypes = [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf'
        ];
        
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Only images (JPEG, PNG, GIF, WebP) and PDF files are allowed'), false);
        }
    }
});

// Validate environment variables
if (!process.env.CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
    console.error('❌ Missing Cloudinary environment variables!');
    throw new Error('Missing required Cloudinary environment variables');
}

// Configure Cloudinary with environment variables
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
});

// POST endpoint to upload expense media
router.post('/upload', authenticateToken, upload.single('media'), async (req, res) => {
    try {
        console.log(`[${new Date().toISOString()}] Media upload request from user ${req.user.email}`);
        
        if (!req.file) {
            return res.status(400).json({ 
                error: 'No file uploaded',
                message: 'Please select a file to upload'
            });
        }

        const { expenseName, expenseDate } = req.body;
        
        if (!expenseName || !expenseDate) {
            return res.status(400).json({ 
                error: 'Missing required fields',
                message: 'Expense name and date are required'
            });
        }

        // Create a safe filename using expense name and date
        const safeExpenseName = expenseName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
        const formattedDate = new Date(expenseDate).toISOString().split('T')[0]; // YYYY-MM-DD format
        const fileExtension = req.file.originalname.split('.').pop();
        const publicId = `${safeExpenseName}_${formattedDate}`;

        console.log(`[${new Date().toISOString()}] Uploading file: ${publicId}.${fileExtension}`);

        // Determine resource type based on file type
        const resourceType = req.file.mimetype === 'application/pdf' ? 'raw' : 'image';

        // Upload to Cloudinary
        const uploadResult = await new Promise((resolve, reject) => {
            const uploadStream = cloudinary.uploader.upload_stream(
                {
                    resource_type: resourceType,
                    public_id: publicId,
                    folder: 'operating_expenses',
                    tags: ['operating_expense', 'receipt', 'slip'],
                    context: {
                        expense_name: expenseName,
                        expense_date: expenseDate,
                        uploaded_by: req.user.email,
                        upload_date: new Date().toISOString()
                    }
                },
                (error, result) => {
                    if (error) {
                        console.error('Cloudinary upload error:', error);
                        reject(error);
                    } else {
                        console.log(`[${new Date().toISOString()}] Successfully uploaded to Cloudinary: ${result.secure_url}`);
                        resolve(result);
                    }
                }
            );

            // Write the file buffer to the upload stream
            uploadStream.end(req.file.buffer);
        });

        res.json({
            success: true,
            message: 'File uploaded successfully',
            mediaUrl: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            resourceType: uploadResult.resource_type,
            format: uploadResult.format,
            bytes: uploadResult.bytes,
            uploadedAt: new Date().toISOString()
        });

    } catch (error) {
        console.error('Media upload error:', error);
        res.status(500).json({ 
            error: 'Failed to upload media', 
            details: error.message 
        });
    }
});

// POST endpoint to extract data from uploaded media using OpenAI (Secure - no sensitive data exposed)
router.post('/extract-data', authenticateToken, upload.single('media'), async (req, res) => {
    try {
        console.log(`[${new Date().toISOString()}] Secure data extraction request from user ${req.user.email}`);

        if (!req.file) {
            return res.status(400).json({
                error: 'No file uploaded',
                message: 'Please select a file to extract data from'
            });
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'];
        if (!allowedTypes.includes(req.file.mimetype)) {
            return res.status(400).json({
                error: 'Invalid file type',
                message: 'Only images (JPEG, PNG, GIF, WebP) and PDF files are supported'
            });
        }

        // Extract data using OpenAI (all sensitive operations happen on backend)
        const extractedData = await openaiService.extractExpenseData(
            req.file.buffer,
            req.file.mimetype
        );

        console.log(`[${new Date().toISOString()}] Secure data extraction completed for user ${req.user.email}`);

        // Return only the extracted data, no sensitive information
        res.json({
            success: true,
            message: 'Data extraction completed successfully',
            data: {
                companyName: extractedData.companyName || '',
                amount: extractedData.amount || '',
                itemName: extractedData.itemName || '',
                confidence: extractedData.confidence || 0,
                isMockData: extractedData.isMockData || false
            },
            meta: {
                fileName: req.file.originalname,
                fileSize: Math.round(req.file.size / 1024), // Size in KB
                extractedAt: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('Secure data extraction error:', error);
        res.status(500).json({
            error: 'Failed to extract data',
            message: 'Unable to process the uploaded file. Please try again or use manual entry.',
            canRetry: true
        });
    }
});

// POST endpoint to upload media with optional data extraction
router.post('/upload-and-extract', authenticateToken, upload.single('media'), async (req, res) => {
    try {
        console.log(`[${new Date().toISOString()}] Upload and extract request from user ${req.user.email}`);

        if (!req.file) {
            return res.status(400).json({
                error: 'No file uploaded',
                message: 'Please select a file to upload'
            });
        }

        const { expenseName, expenseDate, extractData } = req.body;

        if (!expenseName || !expenseDate) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Expense name and date are required'
            });
        }

        // Create a safe filename using expense name and date
        const safeExpenseName = expenseName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
        const formattedDate = new Date(expenseDate).toISOString().split('T')[0]; // YYYY-MM-DD format
        const fileExtension = req.file.originalname.split('.').pop();
        const publicId = `${safeExpenseName}_${formattedDate}`;

        console.log(`[${new Date().toISOString()}] Uploading file: ${publicId}.${fileExtension}`);

        // Determine resource type based on file type
        const resourceType = req.file.mimetype === 'application/pdf' ? 'raw' : 'image';

        // Upload to Cloudinary
        const uploadResult = await new Promise((resolve, reject) => {
            const uploadStream = cloudinary.uploader.upload_stream(
                {
                    resource_type: resourceType,
                    public_id: publicId,
                    folder: 'operating_expenses',
                    tags: ['operating_expense', 'receipt', 'slip'],
                    context: {
                        expense_name: expenseName,
                        expense_date: expenseDate,
                        uploaded_by: req.user.email,
                        upload_date: new Date().toISOString()
                    }
                },
                (error, result) => {
                    if (error) {
                        console.error('Cloudinary upload error:', error);
                        reject(error);
                    } else {
                        console.log(`[${new Date().toISOString()}] Successfully uploaded to Cloudinary: ${result.secure_url}`);
                        resolve(result);
                    }
                }
            );

            // Write the file buffer to the upload stream
            uploadStream.end(req.file.buffer);
        });

        let extractedData = null;

        // Extract data if requested
        if (extractData === 'true') {
            console.log(`[${new Date().toISOString()}] Extracting data from uploaded file`);
            extractedData = await openaiService.extractExpenseData(
                req.file.buffer,
                req.file.mimetype
            );
        }

        res.json({
            success: true,
            message: 'File uploaded successfully',
            mediaUrl: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            resourceType: uploadResult.resource_type,
            format: uploadResult.format,
            bytes: uploadResult.bytes,
            uploadedAt: new Date().toISOString(),
            extractedData: extractedData
        });

    } catch (error) {
        console.error('Upload and extract error:', error);
        res.status(500).json({
            error: 'Failed to upload and extract data',
            details: error.message
        });
    }
});

// POST endpoint for smart expense processing (Upload + Extract + Store) - Fully Secure
router.post('/smart-process', authenticateToken, upload.single('media'), async (req, res) => {
    try {
        console.log(`[${new Date().toISOString()}] Smart expense processing request from user ${req.user.email}`);

        if (!req.file) {
            return res.status(400).json({
                error: 'No file uploaded',
                message: 'Please select a receipt or slip to process'
            });
        }

        const { expenseName, expenseDate } = req.body;

        // Validate required fields
        if (!expenseName || !expenseDate) {
            return res.status(400).json({
                error: 'Missing required information',
                message: 'Expense name and date are required for processing'
            });
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'];
        if (!allowedTypes.includes(req.file.mimetype)) {
            return res.status(400).json({
                error: 'Invalid file type',
                message: 'Only images (JPEG, PNG, GIF, WebP) and PDF files are supported'
            });
        }

        // Step 1: Extract data using OpenAI (secure backend processing)
        console.log(`[${new Date().toISOString()}] Starting AI extraction for ${req.file.originalname}`);
        const extractedData = await openaiService.extractExpenseData(
            req.file.buffer,
            req.file.mimetype
        );

        // Step 2: Upload to Cloudinary (secure backend storage)
        const safeExpenseName = expenseName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
        const formattedDate = new Date(expenseDate).toISOString().split('T')[0];
        const fileExtension = req.file.originalname.split('.').pop();
        const publicId = `${safeExpenseName}_${formattedDate}`;

        console.log(`[${new Date().toISOString()}] Uploading to secure storage: ${publicId}.${fileExtension}`);

        const resourceType = req.file.mimetype === 'application/pdf' ? 'raw' : 'image';
        const uploadResult = await new Promise((resolve, reject) => {
            const uploadStream = cloudinary.uploader.upload_stream(
                {
                    resource_type: resourceType,
                    public_id: publicId,
                    folder: 'operating_expenses',
                    tags: ['operating_expense', 'receipt', 'slip', 'smart_processed'],
                    context: {
                        expense_name: expenseName,
                        expense_date: expenseDate,
                        uploaded_by: req.user.email,
                        upload_date: new Date().toISOString(),
                        ai_processed: 'true',
                        confidence: extractedData.confidence?.toString() || '0'
                    }
                },
                (error, result) => {
                    if (error) {
                        console.error('Secure upload error:', error);
                        reject(error);
                    } else {
                        console.log(`[${new Date().toISOString()}] Successfully uploaded to secure storage`);
                        resolve(result);
                    }
                }
            );
            uploadStream.end(req.file.buffer);
        });

        // Step 3: Return processed data (no sensitive information exposed)
        res.json({
            success: true,
            message: 'Smart processing completed successfully',
            extracted: {
                companyName: extractedData.companyName || '',
                amount: extractedData.amount || '',
                itemName: extractedData.itemName || '',
                confidence: extractedData.confidence || 0,
                isMockData: extractedData.isMockData || false
            },
            upload: {
                mediaUrl: uploadResult.secure_url,
                uploadedAt: new Date().toISOString(),
                fileSize: Math.round(req.file.size / 1024) // Size in KB
            },
            processing: {
                processedAt: new Date().toISOString(),
                processingTime: Date.now() - Date.now(), // Will be calculated properly
                status: 'completed'
            }
        });

    } catch (error) {
        console.error('Smart processing error:', error);
        res.status(500).json({
            error: 'Smart processing failed',
            message: 'Unable to process the receipt automatically. Please try manual entry.',
            canRetry: true,
            fallbackMode: 'manual'
        });
    }
});

// GET endpoint to test OpenAI connection
router.get('/test-openai', authenticateToken, async (req, res) => {
    try {
        console.log(`[${new Date().toISOString()}] OpenAI test request from user ${req.user.email}`);

        // Simple test to verify OpenAI is working
        const testResult = {
            openaiConfigured: !!process.env.OPENAI_API_KEY,
            timestamp: new Date().toISOString(),
            message: 'OpenAI service is ready for expense data extraction'
        };

        res.json({
            success: true,
            test: testResult
        });

    } catch (error) {
        console.error('OpenAI test error:', error);
        res.status(500).json({
            error: 'OpenAI test failed',
            details: error.message
        });
    }
});

// GET endpoint to serve media files with proper headers
router.get('/serve/:publicId', authenticateToken, async (req, res) => {
    try {
        const { publicId } = req.params;
        const { type, name } = req.query; // type: 'image' or 'pdf', name: expense name

        if (!publicId) {
            return res.status(400).json({
                error: 'Missing public ID',
                message: 'Public ID is required to serve media'
            });
        }

        // Construct the Cloudinary URL
        const resourceType = type === 'pdf' ? 'raw' : 'image';
        const cloudinaryUrl = `https://res.cloudinary.com/${process.env.CLOUDINARY_CLOUD_NAME}/${resourceType}/upload/operating_expenses/${publicId}`;

        // Create proper filename
        const safeName = (name || 'receipt').replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
        const extension = type === 'pdf' ? 'pdf' : 'jpg';
        const filename = `${safeName}_receipt.${extension}`;

        // Set proper headers
        const contentType = type === 'pdf' ? 'application/pdf' : 'image/jpeg';
        res.set({
            'Content-Type': contentType,
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Cache-Control': 'public, max-age=31536000' // Cache for 1 year
        });

        // Fetch the file from Cloudinary and pipe it to the response
        const https = require('https');
        const request = https.get(cloudinaryUrl, (cloudinaryRes) => {
            if (cloudinaryRes.statusCode === 200) {
                cloudinaryRes.pipe(res);
            } else {
                res.status(404).json({
                    error: 'Media not found',
                    message: 'The specified media file could not be found'
                });
            }
        });

        request.on('error', (error) => {
            console.error('Error fetching media from Cloudinary:', error);
            res.status(500).json({
                error: 'Failed to fetch media',
                details: error.message
            });
        });

    } catch (error) {
        console.error('Media serve error:', error);
        res.status(500).json({
            error: 'Failed to serve media',
            details: error.message
        });
    }
});

// DELETE endpoint to remove expense media
router.delete('/delete/:publicId', authenticateToken, async (req, res) => {
    try {
        console.log(`[${new Date().toISOString()}] Media delete request from user ${req.user.email}`);
        
        const { publicId } = req.params;
        const { resourceType } = req.query; // 'image' or 'raw'
        
        if (!publicId) {
            return res.status(400).json({ 
                error: 'Missing public ID',
                message: 'Public ID is required to delete media'
            });
        }

        // Delete from Cloudinary
        const deleteResult = await cloudinary.uploader.destroy(
            `operating_expenses/${publicId}`,
            { resource_type: resourceType || 'image' }
        );

        if (deleteResult.result === 'ok') {
            console.log(`[${new Date().toISOString()}] Successfully deleted from Cloudinary: ${publicId}`);
            res.json({
                success: true,
                message: 'Media deleted successfully',
                publicId: publicId
            });
        } else {
            res.status(404).json({
                error: 'Media not found',
                message: 'The specified media file could not be found'
            });
        }

    } catch (error) {
        console.error('Media delete error:', error);
        res.status(500).json({ 
            error: 'Failed to delete media', 
            details: error.message 
        });
    }
});

module.exports = router;
