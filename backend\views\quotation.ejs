<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Quotation - <%= company.name %></title>
    <style>
        /* Basic Reset */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        /* Body Styling */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            padding: 20px;
        }

        /* Responsive Container */
        .container {
            width: 100%;
            max-width: 800px; /* Approximately A4 width */
            margin: auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
        }

        /* Container wrapper */
        .container-wrapper {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .logo {
            width: 150px;
            height: 100px;
            background-image: url('data:image/png;base64,<%= logoBase64 %>');
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
        }

        h1 {
            text-align: center;
            flex-grow: 1;
            margin: 0 20px;
            font-size: 32px;
        }

        /* Company Details */
        .company-details {
            margin-bottom: 20px;
        }

        .company-details h2 {
            margin-bottom: 10px;
            font-size: 24px;
        }

        .address-contact {
            margin-bottom: 10px;
        }

        /* Billing and Shipping */
        .billing-shipping {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .billing, .shipping {
            flex-basis: 48%;
            margin-bottom: 20px;
        }

        .billing h3, .shipping h3 {
            margin-bottom: 10px;
            font-size: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        /* Payment Method Section */
        .payment-method {
            margin-bottom: 20px;
            font-size: 16px;
        }

        /* Table Container Styling */
        .table-container {
            width: 100%;
            margin-bottom: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
            overflow-x: auto; /* Allow horizontal scrolling on desktop */
        }

        /* Div-based Table Styling */
        .div-table {
            display: flex;
            flex-direction: column;
            width: 100%;
            background-color: white;
            font-size: 14px;
            border: 1px solid #e0e0e0;
            margin-bottom: 0;
        }

        .div-table-row {
            display: flex;
            flex-direction: row;
            border-bottom: 1px solid #e0e0e0;
        }

        .div-table-row:nth-child(even) {
            background-color: #f9f9f9;
        }

        .div-table-row:hover {
            background-color: #f0f7ff;
        }

        .div-table-header {
            background-color: #f2f2f2;
            font-weight: bold;
            border-bottom: 2px solid #d0d0d0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .div-table-cell {
            padding: 8px 10px;
            border-right: 1px solid #e0e0e0;
            white-space: nowrap; /* No wrapping on desktop */
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
        }

        .div-table-cell:last-child {
            border-right: none;
        }

        .div-table-heading {
            color: #333;
            font-weight: 700; /* Consistent bold weight */
        }

        /* Column-specific styling */
        .product-column {
            flex: 4;
            min-width: 250px;
            max-width: 350px;
            justify-content: flex-start;
            padding-left: 12px;
        }

        .qty-column {
            flex: 0.3;
            min-width: 30px;
            max-width: 40px;
            justify-content: center;
            font-weight: 700; /* Make it bold */
            text-align: center;
            font-size: 13px;
        }

        .price-column {
            flex: 0.7;
            min-width: 70px;
            justify-content: flex-end;
            padding-right: 12px;
            font-size: 13px;
        }

        /* For the tax column that may be hidden */
        .tax-column {
            display: <%= tax > 0 ? 'flex' : 'none' %>;
        }

        /* Div Table Body */
        .div-table-body {
            display: flex;
            flex-direction: column;
        }

        /* Additional Comments Section */
        .additional-comments {
            margin-bottom: 20px;
        }

        .additional-comments h3 {
            margin-bottom: 10px;
            font-size: 20px;
            /* Removed border-bottom and padding-bottom to eliminate the horizontal line */
            /* border-bottom: 1px solid #ddd; */
            /* padding-bottom: 5px; */
        }

        .additional-comments p {
            font-size: 16px;
            white-space: pre-wrap; /* Preserve line breaks */
        }

        /* Terms Section */
        .terms {
            text-align: left;
            margin-bottom: 20px;
        }

        .terms h3 {
            margin-bottom: 5px;
            font-size: 16px;
        }

        .terms p {
            font-size: 14px;
        }

        /* Totals Section */
        .totals {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        /* Re-enabled Flexbox for totals p elements */
        .totals p {
            display: flex; /* Enable Flexbox */
            justify-content: space-between; /* Space between label and price */
            align-items: center; /* Vertically center align items */
            font-size: 16px;
            margin-bottom: 10px;
        }

        /* Specific styling for Tax Info to ensure Flexbox alignment */
        .totals p.tax {
            display: flex; /* Ensure Flexbox is applied */
            justify-content: space-between; /* Space between label and price */
            align-items: center; /* Vertically center align items */
            font-size: 16px;
            margin-bottom: 10px;
        }

        .totals p span {
            text-align: right; /* Right-align the text within the span */
            font-weight: bold; /* Make the price bold for emphasis */
        }

        .totals p.tax span {
            text-align: right; /* Right-align the text within the span */
            font-weight: bold; /* Make the price bold for emphasis */
        }

        /* Footer */
        .footer {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin-top: 30px;
        }

        /* Responsive Design */
        @media (max-width: 830px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 15px;
            }

            /* Adjust table for medium screens */
            .div-table-cell {
                padding: 6px 8px;
                font-size: 13px;
            }

            /* Adjust column widths for medium screens */
            .product-column {
                min-width: 180px;
                flex: 3;
            }

            .qty-column {
                min-width: 25px;
                flex: 0.3;
                font-size: 12px;
            }

            .price-column {
                min-width: 60px;
                flex: 0.6;
                font-size: 12px;
            }

            /* Adjust header for medium screens */
            h1 { font-size: 28px; }

       

            /* Adjust billing/shipping for medium screens */
            .billing, .shipping {
                width: 100%;
                margin-bottom: 15px;
            }

            .billing-shipping {
                flex-direction: column;
            }
        }

        /* Specific adjustments for small screens */
        @media (max-width: 650px) {
            body {
                padding: 5px;
            }

            .container {
                padding: 12px;
            }

            /* Adjust header for small screens */
            h1 { font-size: 24px; }

        

            /* Adjust table for small screens */
            .table-container {
                overflow-x: visible; /* Disable horizontal scrolling on mobile */
                box-shadow: none;
                border-radius: 0;
            }

            .div-table {
                border-radius: 4px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .div-table-cell {
                padding: 5px 6px;
                font-size: 11px;
            }

            .product-column {
                min-width: 0; /* Remove min-width to allow full flexibility */
                flex: 2;
            }

            .qty-column {
                min-width: 0; /* Remove min-width to allow full flexibility */
                max-width: none;
                flex: 0.25;
                font-size: 11px;
            }

            .price-column {
                min-width: 0; /* Remove min-width to allow full flexibility */
                flex: 0.5;
                font-size: 11px;
            }
        }

        /* Specific adjustments for very small screens */
        @media (max-width: 480px) {
            body {
                padding: 0;
            }

            .container {
                padding: 10px;
            }

            /* Make all table headers consistent size on mobile */
            .div-table-heading {
                font-size: 11px !important; /* Override inline styles */
            }

            /* Mobile-optimized table */
            .div-table-cell {
                padding: 4px 5px;
                font-size: 11px;
                white-space: normal; /* Allow text wrapping on mobile */
            }

            /* Adjust column proportions for mobile */
            .product-column {
                flex: 1.5;
            }

            .qty-column {
                flex: 0.2;
                font-size: 10px;
            }

            .price-column {
                flex: 0.4;
                font-size: 10px;
            }
        }

        /* Print styles */
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            .container {
                box-shadow: none;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container-wrapper">
        <div class="container">
            <div id="quotation">
                <div class="header">
                    <p id="quotation-date">Date: <%= date %></p>
                    <h1>QUOTATION</h1>
                    <div class="logo"></div>
                </div>

            <div class="company-details">
                <h2><%= company.name %></h2>
                <p>61 Civin Drive<br>Bedfordview<br>Johannesburg</p>
                <div class="address-contact">
                    <p><%- company.bankingInformation %></p>
                </div>
                <!-- Reference Number (Non-editable) -->
                <p class="reference-number">Reference Number: <%= referenceNumber %></p>
            </div>

            <div class="billing-shipping">
                <div class="billing">
                    <h3>BILL TO</h3>
                    <p>
                        <strong><%= billing.name %></strong><br>
                        <%= billing.address %><br>
                        Email: <%= billing.email %><br>
                        Phone: <%= billing.phone %>
                    </p>
                </div>
                <div class="shipping">
                    <h3>SHIP TO</h3>
                    <% if (shipping && shipping.name) { %>
                        <p>
                            <strong><%= shipping.name %></strong><br>
                            <%= shipping.address %><br>
                            Email: <%= shipping.email %><br>
                            Phone: <%= shipping.phone %>
                        </p>
                    <% } else { %>
                        <p>Same as billing address</p>
                    <% } %>
                </div>
            </div>

            <!-- Payment Method and Salesperson Section -->
            <p class="payment-method">Payment Method: <span><%= paymentMethod %></span></p>
            <% if (typeof salespersonName !== 'undefined' && salespersonName && salespersonName.trim() !== "") { %>
            <p class="payment-method">Salesperson: <span><%= salespersonName %></span></p>
            <% } %>

            <!-- Products Table (Div-based) -->
            <div class="table-container">
                <div class="div-table">
                    <!-- Table Header -->
                    <div class="div-table-row div-table-header" id="table-header">
                        <div class="div-table-cell div-table-heading product-column">Product</div>
                        <div class="div-table-cell div-table-heading qty-column" style="font-weight: 700; text-align: center;">QTY</div>
                        <div class="div-table-cell div-table-heading price-column">PRICE</div>
                        <div class="div-table-cell div-table-heading price-column tax-column">TAX</div>
                        <div class="div-table-cell div-table-heading price-column">TOTAL</div>
                    </div>
                    <!-- Table Body -->
                    <div class="div-table-body">
                        <% products.forEach(function(product) { %>
                            <div class="div-table-row">
                                <div class="div-table-cell product-column"><%= product.name %></div>
                                <div class="div-table-cell qty-column" style="font-weight: 700; text-align: center;"><%= product.quantity %></div>
                                <div class="div-table-cell price-column">R<%= Math.round(product.unitPriceExcludingTax) %></div>
                                <% if (tax > 0) { %>
                                    <div class="div-table-cell price-column tax-column">R<%= isNaN(parseFloat(product.taxPerUnit)) ? 0 : Math.round(parseFloat(product.taxPerUnit)) %></div>
                                <% } %>
                                <div class="div-table-cell price-column">R<%= Math.round(product.totalPrice) %></div>
                            </div>
                        <% }); %>
                    </div>
                </div>
            </div>

            <!-- Additional Comments Section -->
            <% if (comments && comments.trim() !== "") { %>
                <div class="additional-comments">
                    <h3>Additional Comments:</h3>
                    <p><%= comments.replace(/(?:\r\n|\r|\n)/g, '<br>') %></p>
                </div>
            <% } %>

            <!-- Terms Section -->
            <div class="terms">
                <h3>Terms</h3>
                <p>No refunds or exchanges on correctly supplied items</p>
            </div>

            <!-- Totals Section -->
            <div class="totals">
                <p class="subtotal">Product Amount (Subtotal): <span>R<%= Math.round(subtotal) %></span></p>
                <% if (tax > 0) { %>
                    <p class="tax">Tax (15%): <span>R<%= Math.round(tax) %></span></p>
                <% } %>
                <p class="total">Total Amount: <span>R<%= Math.round(total) %></span></p>
            </div>
        </div>

            <div class="footer">
                <p>Follow us to see more on</p>
                <p>Instagram: @shans_car_accessories</p>
                <p>Twitter/Pinterest: Shans Accessories</p>
                <p>Facebook: Shans Accessories (By Car Brand)</p>
                <p>YouTube/Tik Tok: Shans Accessories All products are Non OEM</p>
            </div>
        </div>
    </div>
</body>
</html>
