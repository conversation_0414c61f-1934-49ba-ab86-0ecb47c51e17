# Database Migrations

This directory contains database migration scripts for the Shans System.

## Running Migrations

To run all migrations, use the following command from the backend directory:

```bash
npm run migrate
```

This will execute all migration scripts in alphabetical order.

## Available Migrations

- `add_categories_table.js` - Creates the categories table and populates it with default categories.

## Creating New Migrations

When creating a new migration:

1. Create a new JavaScript file in this directory with a descriptive name
2. Follow the pattern in existing migrations
3. Make sure to handle the case where the migration has already been applied
4. Add error handling and logging

Example structure:

```javascript
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function myMigration() {
  const client = await pool.connect();
  try {
    console.log('Starting migration: Description of what this migration does...');
    
    // Check if migration is needed
    // Run migration
    // Log success
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration
myMigration()
  .then(() => {
    console.log('Migration completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
```
