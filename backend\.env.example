 Environment Variables for <PERSON><PERSON> Backend
# Copy this file to .env and fill in your actual values

# Database Configuration
DATABASE_URL=your_database_url_here

# Session Secret
SESSION_SECRET=your-session-secret-change-in-production

# JWT Secret
JWT_SECRET=your-jwt-secret-here

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Cloudinary Configuration (for backup functionality)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Server Configuration
PORT=8000
NODE_ENV=development
