// Migration to add media_url column to expenses table
const pool = require('../database');

async function addMediaUrlColumn() {
  let client;
  
  try {
    client = await pool.connect();
    
    console.log('Starting migration: Adding media_url column to expenses table...');
    
    // Check if the column already exists
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'expenses' AND column_name = 'media_url';
    `;
    
    const { rows } = await client.query(checkColumnQuery);
    
    if (rows.length === 0) {
      // Column doesn't exist, so add it
      const addColumnQuery = `
        ALTER TABLE expenses 
        ADD COLUMN media_url TEXT;
      `;
      
      await client.query(addColumnQuery);
      console.log('Migration successful: media_url column added to expenses table.');
    } else {
      console.log('Column media_url already exists in expenses table. No changes made.');
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    if (client) {
      client.release();
    }
  }
}

// Run the migration
if (require.main === module) {
  addMediaUrlColumn()
    .then(() => {
      console.log('Migration completed.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = addMediaUrlColumn;
