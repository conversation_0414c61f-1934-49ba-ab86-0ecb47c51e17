// models/roomModel.js
const pool = require('../database');

const roomModel = {
  createRoom: async (room) => {
    const { name, color, location } = room;
    const query = 'INSERT INTO rooms (name, color, location) VALUES ($1, $2, $3) RETURNING *';
    const values = [name, color, location];
    
    try {
      const { rows } = await pool.query(query, values);
      return rows[0];
    } catch (error) {
      console.error('Error in createRoom:', error);
      throw error;
    }
  },

  getAllRooms: async () => {
    try {
      const { rows } = await pool.query('SELECT * FROM rooms');
      return rows;
    } catch (error) {
      console.error('Error in getAllRooms:', error);
      throw error;
    }
  },

  getRoomById: async (id) => {
    try {
      const query = 'SELECT * FROM rooms WHERE id = $1';
      const { rows } = await pool.query(query, [id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in getRoomById:', error);
      throw error;
    }
  },

  updateRoom: async (id, updatedRoom) => {
    const { name, color, location } = updatedRoom;
    const query = 'UPDATE rooms SET name = $1, color = $2, location = $3 WHERE id = $4 RETURNING *';
    const values = [name, color, location, id];
    
    try {
      const { rows } = await pool.query(query, values);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in updateRoom:', error);
      throw error;
    }
  },

  deleteRoom: async (id) => {
    try {
      const query = 'DELETE FROM rooms WHERE id = $1 RETURNING *';
      const { rows } = await pool.query(query, [id]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteRoom:', error);
      throw error;
    }
  }
};

module.exports = roomModel;