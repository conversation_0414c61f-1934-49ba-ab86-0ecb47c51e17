<body>
    <div class="container-wrapper">
        <div class="container">
            <div id="invoice">
                <div class="header">
                    <p id="invoice-date">Date: <%= date %></p>
                    <h1>INVOICE</h1>
                    <div class="logo"></div>
                </div>

                <div class="company-details">
                    <h2><%= company.name %></h2>
                    <p>61 Civin Drive<br>Bedfordview<br>Johannesburg</p>
                    <div class="address-contact">
                        <p><%- company.bankingInformation %></p>
                    </div>
                    <p class="reference-number">Reference Number: <%= referenceNumber %></p>
                </div>

                <div class="billing-shipping">
                    <div class="billing">
                        <h3>BILL TO</h3>
                        <p>
                            <strong><%= billing.name %></strong><br>
                            <%= billing.address %><br>
                            Email: <%= billing.email %><br>
                            Phone: <%= billing.phone %>
                        </p>
                    </div>
                    <div class="shipping">
                        <h3>SHIP TO</h3>
                        <% if (shipping && shipping.name) { %>
                            <p>
                                <strong><%= shipping.name %></strong><br>
                                <%= shipping.address %><br>
                                Email: <%= shipping.email %><br>
                                Phone: <%= shipping.phone %>
                            </p>
                        <% } else { %>
                            <p>Same as billing address</p>
                        <% } %>
                    </div>
                </div>

                <!-- Payment Method and Salesperson Section -->
                <p class="payment-method">Payment Method: <span><%= paymentMethod %></span></p>
                <% if (typeof salespersonName !== 'undefined' && salespersonName && salespersonName.trim() !== "") { %>
                <p class="payment-method">Salesperson: <span><%= salespersonName %></span></p>
                <% } %>

                <!-- Products Table (Div-based) -->
                <div class="table-container">
                    <div class="div-table">
                        <!-- Table Header -->
                        <div class="div-table-row div-table-header" id="table-header">
                            <div class="div-table-cell div-table-heading product-column">Product</div>
                            <div class="div-table-cell div-table-heading qty-column" style="font-weight: 700; text-align: center;">QTY</div>
                            <div class="div-table-cell div-table-heading price-column">PRICE</div>
                            <div class="div-table-cell div-table-heading price-column tax-column">TAX</div>
                            <div class="div-table-cell div-table-heading price-column">TOTAL</div>
                        </div>
                        <!-- Table Body -->
                        <div class="div-table-body">
                            <% products.forEach(function(product) { %>
                                <div class="div-table-row">
                                    <div class="div-table-cell product-column"><%= product.name %></div>
                                    <div class="div-table-cell qty-column" style="font-weight: 700; text-align: center;"><%= product.quantity %></div>
                                    <div class="div-table-cell price-column">R<%= Math.round(product.unitPriceExcludingTax) %></div>
                                    <% if (tax > 0) { %>
                                        <div class="div-table-cell price-column tax-column">R<%= Math.round(product.taxPerProduct) %></div>
                                    <% } %>
                                    <div class="div-table-cell price-column">R<%= Math.round(product.totalPrice) %></div>
                                </div>
                            <% }); %>
                        </div>
                    </div>
                </div>

                <!-- Additional Comments Section -->
                <% if (comments && comments.trim() !== "") { %>
                    <div class="additional-comments">
                        <h3>Additional Comments:</h3>
                        <p><%- comments.replace(/(?:\r\n|\r|\n)/g, '<br>') %></p>
                    </div>
                <% } %>

                <!-- Terms Section -->
                <div class="terms">
                    <h3>Terms</h3>
                    <p>No refunds or exchanges on correctly supplied items</p>
                </div>

                <!-- Totals Section -->
                <div class="totals">
                    <p class="subtotal">Product Amount (Subtotal): <span>R<%= Math.round(subtotal) %></span></p>
                    <% if (tax > 0) { %>
                        <p class="tax">Tax (15%): <span>R<%= Math.round(tax) %></span></p>
                    <% } %>
                    <p class="total">Total Amount: <span>R<%= Math.round(total) %></span></p>
                </div>
            </div>

            <div class="footer">
                <p>Follow us to see more on</p>
                <p>Instagram: @shans_car_accessories</p>
                <p>Twitter/Pinterest: Shans Accessories</p>
                <p>Facebook: Shans Accessories (By Car Brand)</p>
                <p>YouTube/Tik Tok: Shans Accessories All products are Non OEM</p>
            </div>
        </div>
    </div>
</body>
</html>
