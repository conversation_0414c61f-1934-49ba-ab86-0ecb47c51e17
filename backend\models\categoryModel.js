// models/categoryModel.js
const pool = require('../database');

const categoryModel = {
  createCategory: async (category) => {
    const { category_id, name } = category;
    const query = 'INSERT INTO categories (category_id, name) VALUES ($1, $2) RETURNING *';
    const values = [category_id, name];
    
    try {
      const { rows } = await pool.query(query, values);
      return rows[0];
    } catch (error) {
      console.error('Error in createCategory:', error);
      throw error;
    }
  },

  getAllCategories: async () => {
    try {
      const query = 'SELECT * FROM categories ORDER BY name';
      const { rows } = await pool.query(query);
      return rows;
    } catch (error) {
      console.error('Error in getAllCategories:', error);
      throw error;
    }
  },

  getCategoryById: async (category_id) => {
    try {
      const query = 'SELECT * FROM categories WHERE category_id = $1';
      const { rows } = await pool.query(query, [category_id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in getCategoryById:', error);
      throw error;
    }
  },

  updateCategory: async (category_id, name) => {
    try {
      const query = 'UPDATE categories SET name = $1 WHERE category_id = $2 RETURNING *';
      const { rows } = await pool.query(query, [name, category_id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in updateCategory:', error);
      throw error;
    }
  },

  deleteCategory: async (category_id) => {
    try {
      const query = 'DELETE FROM categories WHERE category_id = $1 RETURNING *';
      const { rows } = await pool.query(query, [category_id]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteCategory:', error);
      throw error;
    }
  },

  // Check if any products are using this category
  isCategoryInUse: async (category_id) => {
    try {
      const query = 'SELECT COUNT(*) FROM products WHERE product_category = $1';
      const { rows } = await pool.query(query, [category_id]);
      return parseInt(rows[0].count) > 0;
    } catch (error) {
      console.error('Error in isCategoryInUse:', error);
      throw error;
    }
  }
};

module.exports = categoryModel;
