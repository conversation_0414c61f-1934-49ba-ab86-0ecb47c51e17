# Setting Up Categories Table

This document provides instructions for setting up the categories table in the database.

## Option 1: Using the Migration System

Run the following command from the backend directory:

```bash
npm run migrate
```

This will execute all migration scripts, including the one that creates the categories table.

## Option 2: Direct Migration

If Option 1 doesn't work, you can run the categories migration directly:

```bash
npm run migrate-categories
```

This will only run the migration script for the categories table.

## Option 3: Manual Creation

If both migration approaches fail, you can manually create the categories table:

```bash
npm run create-categories
```

This script directly creates the categories table and inserts the default categories.

## Verifying the Setup

After running one of the above commands, you can verify that the categories table was created by:

1. Starting the backend server: `npm run dev` or `node index.js`
2. Opening the ManageProducts.html page in your browser
3. Clicking on "Manage Categories" to see if the default categories are displayed
4. Adding a new category and refreshing the page to verify it persists

## Troubleshooting

If you encounter issues:

1. Check the console output for error messages
2. Verify that your .env file contains the correct DATABASE_URL
3. Make sure the database user has permissions to create tables
4. Try running the scripts with administrator/elevated privileges if needed

If all else fails, you can manually create the table in your database using the following SQL:

```sql
CREATE TABLE IF NOT EXISTS categories (
  id SERIAL PRIMARY KEY,
  category_id TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default categories
INSERT INTO categories (category_id, name) VALUES ('lips', 'Lips') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('boot_spoilers', 'Boot spoilers') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('roof_spoilers', 'Roof spoilers') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('diffusers', 'Diffusers') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('sills_extensions', 'Sills extensions') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('eyelids', 'Eyelids') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('mirrors_covers', 'Mirrors covers') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('grills', 'Grills') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('canards', 'Canards') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('bumper_spats', 'Bumper spats') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('arches', 'Arches') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('step_bars', 'Step bars') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('bonnet_scoops', 'Bonnet scoops') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('roll_bars', 'Roll bars') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('fog_grills', 'Fog grills') ON CONFLICT (category_id) DO NOTHING;
INSERT INTO categories (category_id, name) VALUES ('vinyl', 'Vinyl') ON CONFLICT (category_id) DO NOTHING;
```
